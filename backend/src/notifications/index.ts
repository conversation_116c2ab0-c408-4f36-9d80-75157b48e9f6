import { APIGatewayProxyEvent, APIGatewayProxyResult, Context } from 'aws-lambda';
import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { DynamoDBDocumentClient, GetCommand, PutCommand, QueryCommand, UpdateCommand } from '@aws-sdk/lib-dynamodb';
import { SNSClient, PublishCommand } from '@aws-sdk/client-sns';
import { v4 as uuidv4 } from 'uuid';

// Configure AWS SDK
const awsConfig = {
    region: process.env.AWS_REGION || 'us-west-2'
};

const dynamodbClient = new DynamoDBClient(awsConfig);
const dynamodb = DynamoDBDocumentClient.from(dynamodbClient);
const snsClient = new SNSClient(awsConfig);

// Environment variables
const NOTIFICATIONS_TABLE = process.env.NOTIFICATIONS_TABLE!;
const DEVICE_TOKENS_TABLE = process.env.DEVICE_TOKENS_TABLE!;
const NOTIFICATION_PREFERENCES_TABLE = process.env.NOTIFICATION_PREFERENCES_TABLE!;
const NOTIFICATION_HISTORY_TABLE = process.env.NOTIFICATION_HISTORY_TABLE!;
const SNS_TOPIC_ARN = process.env.SNS_TOPIC_ARN!;

// Notification types
export enum NotificationType {
    FOLLOW = 'follow',
    REFLEX = 'reflex',
    COMMENT = 'comment'
}

// Rate limiting configuration (per user per type)
const RATE_LIMITS = {
    [NotificationType.FOLLOW]: {
        maxPerHour: 10,
        maxPerDay: 50
    },
    [NotificationType.REFLEX]: {
        maxPerHour: 20,
        maxPerDay: 100
    },
    [NotificationType.COMMENT]: {
        maxPerHour: 30,
        maxPerDay: 150
    }
};

// Default notification preferences
const DEFAULT_PREFERENCES = {
    [NotificationType.FOLLOW]: true,
    [NotificationType.REFLEX]: true,
    [NotificationType.COMMENT]: true,
    pushEnabled: true,
    emailEnabled: false,
    quietHoursStart: '22:00',
    quietHoursEnd: '08:00',
    timezone: 'UTC'
};

interface NotificationPayload {
    type: NotificationType;
    recipientUserId: string;
    actorUserId: string;
    actorUsername?: string;
    actorDisplayName?: string;
    actorAvatarUrl?: string;
    title: string;
    body: string;
    data?: Record<string, any>;
}

interface DeviceToken {
    userId: string;
    deviceId: string;
    token: string;
    platform: 'ios' | 'android';
    isActive: boolean;
    createdAt: string;
    updatedAt: string;
}

interface NotificationPreferences {
    userId: string;
    follow: boolean;
    reflex: boolean;
    comment: boolean;
    pushEnabled: boolean;
    emailEnabled: boolean;
    quietHoursStart: string;
    quietHoursEnd: string;
    timezone: string;
    updatedAt: string;
}

// Helper function to create API response
const createResponse = (statusCode: number, body: any): APIGatewayProxyResult => ({
    statusCode,
    headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token',
        'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
    },
    body: JSON.stringify(body)
});

// Get user ID from authorizer context
const getUserIdFromContext = (event: APIGatewayProxyEvent): string | null => {
    return event.requestContext?.authorizer?.userId || null;
};

// Check if user is in quiet hours
const isInQuietHours = (preferences: NotificationPreferences): boolean => {
    const now = new Date();
    const userTime = new Date(now.toLocaleString("en-US", { timeZone: preferences.timezone }));
    const currentHour = userTime.getHours();
    const currentMinute = userTime.getMinutes();
    const currentTime = currentHour * 60 + currentMinute;

    const [startHour, startMinute] = preferences.quietHoursStart.split(':').map(Number);
    const [endHour, endMinute] = preferences.quietHoursEnd.split(':').map(Number);
    const startTime = startHour * 60 + startMinute;
    const endTime = endHour * 60 + endMinute;

    if (startTime <= endTime) {
        // Same day quiet hours (e.g., 22:00 to 23:59)
        return currentTime >= startTime && currentTime <= endTime;
    } else {
        // Overnight quiet hours (e.g., 22:00 to 08:00)
        return currentTime >= startTime || currentTime <= endTime;
    }
};

// Check rate limits for notifications
const checkRateLimit = async (userId: string, type: NotificationType): Promise<boolean> => {
    const now = new Date();
    const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);
    const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);

    try {
        // Query recent notifications of this type
        const queryCommand = new QueryCommand({
            TableName: NOTIFICATION_HISTORY_TABLE,
            IndexName: 'userId-type-index',
            KeyConditionExpression: 'userId = :userId AND #type = :type',
            FilterExpression: '#timestamp > :oneHourAgo',
            ExpressionAttributeNames: {
                '#type': 'type',
                '#timestamp': 'timestamp'
            },
            ExpressionAttributeValues: {
                ':userId': userId,
                ':type': type,
                ':oneHourAgo': oneHourAgo.toISOString()
            }
        });

        const hourlyResult = await dynamodb.send(queryCommand);
        const hourlyCount = hourlyResult.Items?.length || 0;

        if (hourlyCount >= RATE_LIMITS[type].maxPerHour) {
            console.log(`Rate limit exceeded for user ${userId}, type ${type}: ${hourlyCount} notifications in the last hour`);
            return false;
        }

        // Check daily limit
        const dailyQueryCommand = new QueryCommand({
            TableName: NOTIFICATION_HISTORY_TABLE,
            IndexName: 'userId-type-index',
            KeyConditionExpression: 'userId = :userId AND #type = :type',
            FilterExpression: '#timestamp > :oneDayAgo',
            ExpressionAttributeNames: {
                '#type': 'type',
                '#timestamp': 'timestamp'
            },
            ExpressionAttributeValues: {
                ':userId': userId,
                ':type': type,
                ':oneDayAgo': oneDayAgo.toISOString()
            }
        });

        const dailyResult = await dynamodb.send(dailyQueryCommand);
        const dailyCount = dailyResult.Items?.length || 0;

        if (dailyCount >= RATE_LIMITS[type].maxPerDay) {
            console.log(`Daily rate limit exceeded for user ${userId}, type ${type}: ${dailyCount} notifications in the last day`);
            return false;
        }

        return true;
    } catch (error) {
        console.error('Error checking rate limit:', error);
        return false; // Fail safe - don't send if we can't check
    }
};

// Get user notification preferences
const getUserPreferences = async (userId: string): Promise<NotificationPreferences> => {
    try {
        const getCommand = new GetCommand({
            TableName: NOTIFICATION_PREFERENCES_TABLE,
            Key: { userId }
        });

        const result = await dynamodb.send(getCommand);

        if (result.Item) {
            return result.Item as NotificationPreferences;
        }

        // Return default preferences if none exist
        return {
            userId,
            ...DEFAULT_PREFERENCES,
            updatedAt: new Date().toISOString()
        };
    } catch (error) {
        console.error('Error getting user preferences:', error);
        // Return default preferences on error
        return {
            userId,
            ...DEFAULT_PREFERENCES,
            updatedAt: new Date().toISOString()
        };
    }
};

// Get user device tokens
const getUserDeviceTokens = async (userId: string): Promise<DeviceToken[]> => {
    try {
        const queryCommand = new QueryCommand({
            TableName: DEVICE_TOKENS_TABLE,
            KeyConditionExpression: 'userId = :userId',
            FilterExpression: 'isActive = :isActive',
            ExpressionAttributeValues: {
                ':userId': userId,
                ':isActive': true
            }
        });

        const result = await dynamodb.send(queryCommand);
        return (result.Items || []) as DeviceToken[];
    } catch (error) {
        console.error('Error getting device tokens:', error);
        return [];
    }
};

// Send push notification via SNS
const sendPushNotification = async (deviceToken: DeviceToken, payload: NotificationPayload): Promise<boolean> => {
    try {
        const message = {
            default: payload.body,
            GCM: JSON.stringify({
                notification: {
                    title: payload.title,
                    body: payload.body,
                },
                data: {
                    type: payload.type,
                    actorUserId: payload.actorUserId,
                    ...payload.data
                }
            }),
            APNS: JSON.stringify({
                aps: {
                    alert: {
                        title: payload.title,
                        body: payload.body,
                    },
                    badge: 1,
                    sound: 'default'
                },
                type: payload.type,
                actorUserId: payload.actorUserId,
                ...payload.data
            })
        };

        const publishCommand = new PublishCommand({
            TopicArn: SNS_TOPIC_ARN,
            Message: JSON.stringify(message),
            MessageStructure: 'json',
            MessageAttributes: {
                platform: {
                    DataType: 'String',
                    StringValue: deviceToken.platform
                },
                userId: {
                    DataType: 'String',
                    StringValue: deviceToken.userId
                }
            }
        });

        await snsClient.send(publishCommand);
        console.log(`Push notification sent to ${deviceToken.platform} device ${deviceToken.deviceId}`);
        return true;
    } catch (error) {
        console.error('Error sending push notification:', error);
        return false;
    }
};

// Main notification sending function
export const sendNotification = async (payload: NotificationPayload): Promise<boolean> => {
    try {
        console.log(`Processing notification: ${payload.type} for user ${payload.recipientUserId} from ${payload.actorUserId}`);

        // Don't send notifications to self
        if (payload.recipientUserId === payload.actorUserId) {
            console.log('Skipping self-notification');
            return false;
        }

        // Check rate limits
        const rateLimitOk = await checkRateLimit(payload.recipientUserId, payload.type);
        if (!rateLimitOk) {
            console.log('Rate limit exceeded, skipping notification');
            return false;
        }

        // Get user preferences
        const preferences = await getUserPreferences(payload.recipientUserId);

        // Check if user has this notification type enabled
        if (!preferences[payload.type] || !preferences.pushEnabled) {
            console.log(`User has disabled ${payload.type} notifications or push notifications`);
            return false;
        }

        // Check quiet hours
        if (isInQuietHours(preferences)) {
            console.log('User is in quiet hours, skipping notification');
            return false;
        }

        // Get device tokens
        const deviceTokens = await getUserDeviceTokens(payload.recipientUserId);
        if (deviceTokens.length === 0) {
            console.log('No active device tokens found for user');
            return false;
        }

        // Create notification record
        const notificationId = uuidv4();
        const now = new Date().toISOString();

        const notificationRecord = {
            id: notificationId,
            userId: payload.recipientUserId,
            type: payload.type,
            actorUserId: payload.actorUserId,
            actorUsername: payload.actorUsername,
            actorDisplayName: payload.actorDisplayName,
            actorAvatarUrl: payload.actorAvatarUrl,
            title: payload.title,
            body: payload.body,
            data: payload.data,
            isRead: false,
            createdAt: now,
            updatedAt: now
        };

        // Save notification to database
        const putNotificationCommand = new PutCommand({
            TableName: NOTIFICATIONS_TABLE,
            Item: notificationRecord
        });
        await dynamodb.send(putNotificationCommand);

        // Record in notification history for rate limiting
        const historyRecord = {
            userId: payload.recipientUserId,
            timestamp: now,
            type: payload.type,
            notificationId,
            ttl: Math.floor(Date.now() / 1000) + (30 * 24 * 60 * 60) // 30 days TTL
        };

        const putHistoryCommand = new PutCommand({
            TableName: NOTIFICATION_HISTORY_TABLE,
            Item: historyRecord
        });
        await dynamodb.send(putHistoryCommand);

        // Send push notifications to all devices
        let sentCount = 0;
        for (const deviceToken of deviceTokens) {
            const sent = await sendPushNotification(deviceToken, payload);
            if (sent) sentCount++;
        }

        console.log(`Notification sent to ${sentCount}/${deviceTokens.length} devices`);
        return sentCount > 0;

    } catch (error) {
        console.error('Error sending notification:', error);
        return false;
    }
};

// Lambda handler for API Gateway events
export const handler = async (event: APIGatewayProxyEvent, context: Context): Promise<APIGatewayProxyResult> => {
    console.log('Notification service event:', JSON.stringify(event, null, 2));

    try {
        const path = event.path;
        const method = event.httpMethod;

        // Handle different API endpoints
        if (path.includes('/notifications') && method === 'GET') {
            return await getUserNotifications(event);
        } else if (path.includes('/notifications') && method === 'PUT') {
            return await markNotificationAsRead(event);
        } else if (path.includes('/device-tokens') && method === 'POST') {
            return await registerDeviceToken(event);
        } else if (path.includes('/device-tokens') && method === 'DELETE') {
            return await unregisterDeviceToken(event);
        } else if (path.includes('/preferences') && method === 'GET') {
            return await getNotificationPreferences(event);
        } else if (path.includes('/preferences') && method === 'PUT') {
            return await updateNotificationPreferences(event);
        }

        return createResponse(404, { error: 'Endpoint not found' });

    } catch (error) {
        console.error('Handler error:', error);
        return createResponse(500, { error: 'Internal server error', details: (error as Error).message });
    }
};

// Get user notifications
const getUserNotifications = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
        const userId = getUserIdFromContext(event);
        if (!userId) {
            return createResponse(401, { error: 'Unauthorized' });
        }

        const { limit = '20', lastKey } = event.queryStringParameters || {};
        const limitNum = Math.min(parseInt(limit), 50); // Max 50 notifications per request

        const queryParams: any = {
            TableName: NOTIFICATIONS_TABLE,
            IndexName: 'userId-createdAt-index',
            KeyConditionExpression: 'userId = :userId',
            ExpressionAttributeValues: {
                ':userId': userId
            },
            ScanIndexForward: false, // Most recent first
            Limit: limitNum
        };

        if (lastKey) {
            queryParams.ExclusiveStartKey = JSON.parse(decodeURIComponent(lastKey));
        }

        const queryCommand = new QueryCommand(queryParams);
        const result = await dynamodb.send(queryCommand);

        return createResponse(200, {
            notifications: result.Items || [],
            lastKey: result.LastEvaluatedKey ? encodeURIComponent(JSON.stringify(result.LastEvaluatedKey)) : null,
            hasMore: !!result.LastEvaluatedKey
        });

    } catch (error) {
        console.error('GetUserNotifications error:', error);
        return createResponse(500, { error: 'Failed to get notifications', details: (error as Error).message });
    }
};

// Mark notification as read
const markNotificationAsRead = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
        const userId = getUserIdFromContext(event);
        if (!userId) {
            return createResponse(401, { error: 'Unauthorized' });
        }

        const notificationId = event.pathParameters?.id;
        if (!notificationId) {
            return createResponse(400, { error: 'Notification ID is required' });
        }

        // First verify the notification belongs to the user
        const getCommand = new GetCommand({
            TableName: NOTIFICATIONS_TABLE,
            Key: { id: notificationId }
        });

        const result = await dynamodb.send(getCommand);
        if (!result.Item) {
            return createResponse(404, { error: 'Notification not found' });
        }

        if (result.Item.userId !== userId) {
            return createResponse(403, { error: 'Access denied' });
        }

        // Update notification as read
        const updateCommand = new UpdateCommand({
            TableName: NOTIFICATIONS_TABLE,
            Key: { id: notificationId },
            UpdateExpression: 'SET isRead = :isRead, updatedAt = :updatedAt',
            ExpressionAttributeValues: {
                ':isRead': true,
                ':updatedAt': new Date().toISOString()
            }
        });

        await dynamodb.send(updateCommand);

        return createResponse(200, { message: 'Notification marked as read' });

    } catch (error) {
        console.error('MarkNotificationAsRead error:', error);
        return createResponse(500, { error: 'Failed to mark notification as read', details: (error as Error).message });
    }
};

// Register device token
const registerDeviceToken = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
        const userId = getUserIdFromContext(event);
        if (!userId) {
            return createResponse(401, { error: 'Unauthorized' });
        }

        const body = JSON.parse(event.body || '{}');
        const { deviceId, token, platform } = body;

        if (!deviceId || !token || !platform) {
            return createResponse(400, { error: 'deviceId, token, and platform are required' });
        }

        if (!['ios', 'android'].includes(platform)) {
            return createResponse(400, { error: 'platform must be ios or android' });
        }

        const now = new Date().toISOString();
        const deviceToken: DeviceToken = {
            userId,
            deviceId,
            token,
            platform,
            isActive: true,
            createdAt: now,
            updatedAt: now
        };

        const putCommand = new PutCommand({
            TableName: DEVICE_TOKENS_TABLE,
            Item: deviceToken
        });

        await dynamodb.send(putCommand);

        return createResponse(200, { message: 'Device token registered successfully' });

    } catch (error) {
        console.error('RegisterDeviceToken error:', error);
        return createResponse(500, { error: 'Failed to register device token', details: (error as Error).message });
    }
};

// Unregister device token
const unregisterDeviceToken = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
        const userId = getUserIdFromContext(event);
        if (!userId) {
            return createResponse(401, { error: 'Unauthorized' });
        }

        const deviceId = event.pathParameters?.deviceId;
        if (!deviceId) {
            return createResponse(400, { error: 'Device ID is required' });
        }

        const updateCommand = new UpdateCommand({
            TableName: DEVICE_TOKENS_TABLE,
            Key: { userId, deviceId },
            UpdateExpression: 'SET isActive = :isActive, updatedAt = :updatedAt',
            ExpressionAttributeValues: {
                ':isActive': false,
                ':updatedAt': new Date().toISOString()
            }
        });

        await dynamodb.send(updateCommand);

        return createResponse(200, { message: 'Device token unregistered successfully' });

    } catch (error) {
        console.error('UnregisterDeviceToken error:', error);
        return createResponse(500, { error: 'Failed to unregister device token', details: (error as Error).message });
    }
};

// Get notification preferences
const getNotificationPreferences = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
        const userId = getUserIdFromContext(event);
        if (!userId) {
            return createResponse(401, { error: 'Unauthorized' });
        }

        const preferences = await getUserPreferences(userId);
        return createResponse(200, preferences);

    } catch (error) {
        console.error('GetNotificationPreferences error:', error);
        return createResponse(500, { error: 'Failed to get notification preferences', details: (error as Error).message });
    }
};

// Update notification preferences
const updateNotificationPreferences = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
        const userId = getUserIdFromContext(event);
        if (!userId) {
            return createResponse(401, { error: 'Unauthorized' });
        }

        const body = JSON.parse(event.body || '{}');
        const {
            follow,
            reflex,
            comment,
            pushEnabled,
            emailEnabled,
            quietHoursStart,
            quietHoursEnd,
            timezone
        } = body;

        // Validate time format if provided
        const timeRegex = /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/;
        if (quietHoursStart && !timeRegex.test(quietHoursStart)) {
            return createResponse(400, { error: 'Invalid quietHoursStart format. Use HH:MM' });
        }
        if (quietHoursEnd && !timeRegex.test(quietHoursEnd)) {
            return createResponse(400, { error: 'Invalid quietHoursEnd format. Use HH:MM' });
        }

        const now = new Date().toISOString();
        const preferences: NotificationPreferences = {
            userId,
            follow: follow !== undefined ? follow : DEFAULT_PREFERENCES.follow,
            reflex: reflex !== undefined ? reflex : DEFAULT_PREFERENCES.reflex,
            comment: comment !== undefined ? comment : DEFAULT_PREFERENCES.comment,
            pushEnabled: pushEnabled !== undefined ? pushEnabled : DEFAULT_PREFERENCES.pushEnabled,
            emailEnabled: emailEnabled !== undefined ? emailEnabled : DEFAULT_PREFERENCES.emailEnabled,
            quietHoursStart: quietHoursStart || DEFAULT_PREFERENCES.quietHoursStart,
            quietHoursEnd: quietHoursEnd || DEFAULT_PREFERENCES.quietHoursEnd,
            timezone: timezone || DEFAULT_PREFERENCES.timezone,
            updatedAt: now
        };

        const putCommand = new PutCommand({
            TableName: NOTIFICATION_PREFERENCES_TABLE,
            Item: preferences
        });

        await dynamodb.send(putCommand);

        return createResponse(200, { message: 'Notification preferences updated successfully', preferences });

    } catch (error) {
        console.error('UpdateNotificationPreferences error:', error);
        return createResponse(500, { error: 'Failed to update notification preferences', details: (error as Error).message });
    }
};
