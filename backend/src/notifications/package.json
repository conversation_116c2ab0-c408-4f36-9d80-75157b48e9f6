{"name": "notifications-function", "version": "1.0.0", "description": "GameFlex Notifications Lambda Function", "main": "index.js", "scripts": {"prebuild": "cp -r ../utils ./utils 2>/dev/null || true", "build": "npm run prebuild && tsc", "clean": "rm -rf node_modules dist utils"}, "dependencies": {"@aws-sdk/client-dynamodb": "^3.0.0", "@aws-sdk/client-sns": "^3.0.0", "@aws-sdk/lib-dynamodb": "^3.0.0", "uuid": "^9.0.0"}, "devDependencies": {"@types/aws-lambda": "^8.10.0", "@types/node": "^18.0.0", "@types/uuid": "^9.0.0", "typescript": "^5.0.0"}}