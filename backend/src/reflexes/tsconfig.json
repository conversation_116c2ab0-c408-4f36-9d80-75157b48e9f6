{"compilerOptions": {"target": "ES2020", "module": "commonjs", "lib": ["ES2020"], "outDir": "./dist", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "declaration": true, "declarationMap": true, "sourceMap": true, "resolveJsonModule": true, "moduleResolution": "node"}, "include": ["*.ts", "utils/**/*.ts"], "exclude": ["node_modules", "dist", "**/*.test.ts"]}