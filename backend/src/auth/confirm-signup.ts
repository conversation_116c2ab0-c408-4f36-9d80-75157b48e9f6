import {
    CognitoIdentityProviderClient,
    ConfirmSignUpCommand,
    type ConfirmSignUpCommandInput
} from '@aws-sdk/client-cognito-identity-provider';
import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';

// Types
interface ConfirmSignUpRequest {
    email: string;
    confirmationCode: string;
}

// Configure AWS SDK
const awsConfig = {
    region: process.env.AWS_REGION || 'us-west-2'
};

const cognitoClient = new CognitoIdentityProviderClient(awsConfig);

// Environment variables
const USER_POOL_CLIENT_ID = process.env.USER_POOL_CLIENT_ID || process.env.USERPOOLCLIENT_USER_POOL_CLIENT_ID;

// Helper function to create response
const createResponse = (statusCode: number, body: any): APIGatewayProxyResult => ({
    statusCode,
    headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token',
        'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
    },
    body: JSON.stringify(body)
});

// Confirm signup handler
export const handler = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    console.log('ConfirmSignUp Event:', JSON.stringify(event, null, 2));

    // Handle CORS preflight requests
    if (event.httpMethod === 'OPTIONS') {
        return createResponse(200, {});
    }

    try {
        if (!event.body) {
            return createResponse(400, { error: 'Request body is required' });
        }

        const { email, confirmationCode }: ConfirmSignUpRequest = JSON.parse(event.body);

        if (!email || !confirmationCode) {
            return createResponse(400, { error: 'Email and confirmation code are required' });
        }

        // Normalize email to lowercase for case-insensitive handling
        const normalizedEmail = email.toLowerCase();

        if (!USER_POOL_CLIENT_ID) {
            return createResponse(500, { error: 'User pool client configuration missing' });
        }

        // Confirm the user's signup
        const confirmParams: ConfirmSignUpCommandInput = {
            ClientId: USER_POOL_CLIENT_ID,
            Username: normalizedEmail,
            ConfirmationCode: confirmationCode
        };

        const confirmCommand = new ConfirmSignUpCommand(confirmParams);
        await cognitoClient.send(confirmCommand);

        return createResponse(200, {
            message: 'Email verified successfully. You can now sign in to your account.',
            verified: true
        });

    } catch (error: any) {
        console.error('ConfirmSignUp error:', error);

        // Handle specific Cognito errors
        if (error.name === 'CodeMismatchException') {
            return createResponse(400, {
                error: 'Invalid verification code',
                details: 'The verification code you entered is incorrect. Please check and try again.'
            });
        } else if (error.name === 'ExpiredCodeException') {
            return createResponse(400, {
                error: 'Verification code expired',
                details: 'The verification code has expired. Please request a new one.'
            });
        } else if (error.name === 'UserNotFoundException') {
            return createResponse(404, {
                error: 'User not found',
                details: 'No account found with this email address.'
            });
        } else if (error.name === 'NotAuthorizedException') {
            return createResponse(400, {
                error: 'User already confirmed',
                details: 'This account has already been verified. You can sign in now.'
            });
        } else if (error.name === 'TooManyRequestsException') {
            return createResponse(429, {
                error: 'Too many attempts',
                details: 'Too many verification attempts. Please try again later.'
            });
        } else {
            return createResponse(500, {
                error: 'Verification failed',
                details: 'An unexpected error occurred. Please try again.'
            });
        }
    }
};