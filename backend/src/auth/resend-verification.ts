import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { CognitoIdentityProviderClient, ResendConfirmationCodeCommand } from '@aws-sdk/client-cognito-identity-provider';

// Initialize Cognito client
const cognitoClient = new CognitoIdentityProviderClient({ region: process.env.AWS_REGION });

// Environment variables
const USER_POOL_CLIENT_ID = process.env.USER_POOL_CLIENT_ID;

interface ResendVerificationRequest {
    email: string;
}

// Helper function to create response
const createResponse = (statusCode: number, body: any): APIGatewayProxyResult => ({
    statusCode,
    headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token',
        'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
    },
    body: JSON.stringify(body)
});

// Resend verification email handler
export const handler = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    console.log('ResendVerification Event:', JSON.stringify(event, null, 2));

    // Handle CORS preflight requests
    if (event.httpMethod === 'OPTIONS') {
        return createResponse(200, {});
    }

    try {
        if (!event.body) {
            return createResponse(400, { error: 'Request body is required' });
        }

        const { email }: ResendVerificationRequest = JSON.parse(event.body);

        if (!email) {
            return createResponse(400, { error: 'Email is required' });
        }

        // Normalize email to lowercase for case-insensitive handling
        const normalizedEmail = email.toLowerCase();

        if (!USER_POOL_CLIENT_ID) {
            return createResponse(500, { error: 'Cognito configuration missing' });
        }

        // Resend confirmation code
        const resendCommand = new ResendConfirmationCodeCommand({
            ClientId: USER_POOL_CLIENT_ID,
            Username: normalizedEmail,
        });

        await cognitoClient.send(resendCommand);

        console.log(`Verification email resent successfully for: ${normalizedEmail}`);

        return createResponse(200, {
            message: 'Verification email sent successfully',
            details: 'Please check your email for the verification code.'
        });

    } catch (error: any) {
        console.error('ResendVerification Error:', error);

        // Handle specific Cognito errors
        if (error.name === 'UserNotFoundException') {
            return createResponse(404, {
                error: 'User not found',
                details: 'No account found with this email address.'
            });
        } else if (error.name === 'InvalidParameterException') {
            return createResponse(400, {
                error: 'Invalid request',
                details: 'Please check your email format.'
            });
        } else if (error.name === 'LimitExceededException') {
            return createResponse(429, {
                error: 'Too many requests',
                details: 'Too many verification emails sent. Please wait before requesting another.'
            });
        } else if (error.name === 'NotAuthorizedException') {
            return createResponse(400, {
                error: 'User already confirmed',
                details: 'This account is already verified. You can sign in normally.'
            });
        } else {
            return createResponse(500, {
                error: 'Failed to send verification email',
                details: 'An unexpected error occurred. Please try again.'
            });
        }
    }
};
