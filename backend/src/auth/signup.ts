import {
    CognitoIdentityProviderClient,
    SignUpCommand,
    AdminConfirmSignUpCommand,
    type SignUpCommandInput
} from '@aws-sdk/client-cognito-identity-provider';
import {
    DynamoDBClient
} from '@aws-sdk/client-dynamodb';
import {
    DynamoDBDocumentClient,
    PutCommand
} from '@aws-sdk/lib-dynamodb';
import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';

// Types
interface SignUpRequest {
    email: string;
    password: string;
    firstName?: string;
    lastName?: string;
}

interface UserRecord {
    id: string;
    email: string;
    username?: string;
    firstName: string;
    lastName: string;
    cognitoUserId: string;
    createdAt: string;
    updatedAt: string;
}

// Configure AWS SDK
const awsConfig = {
    region: process.env.AWS_REGION || 'us-west-2'
};

if (process.env.AWS_SAM_LOCAL) {
    console.log('Running in SAM Local mode');
}

const cognitoClient = new CognitoIdentityProviderClient(awsConfig);
const dynamoClient = new DynamoDBClient(awsConfig);
const dynamodb = DynamoDBDocumentClient.from(dynamoClient);

// Environment variables
const USER_POOL_ID = process.env.USER_POOL_ID || process.env.USERPOOL_USER_POOL_ID;
const USER_POOL_CLIENT_ID = process.env.USER_POOL_CLIENT_ID || process.env.USERPOOLCLIENT_USER_POOL_CLIENT_ID;
const USERS_TABLE = process.env.USERS_TABLE || process.env.USERSTABLE_TABLE_NAME;

// Helper function to create response
const createResponse = (statusCode: number, body: any): APIGatewayProxyResult => ({
    statusCode,
    headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token',
        'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
    },
    body: JSON.stringify(body)
});

// Sign up handler
export const handler = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    console.log('SignUp Event:', JSON.stringify(event, null, 2));

    // Handle CORS preflight requests
    if (event.httpMethod === 'OPTIONS') {
        return createResponse(200, {});
    }

    try {
        if (!event.body) {
            return createResponse(400, { error: 'Request body is required' });
        }

        const { email, password, firstName, lastName }: SignUpRequest = JSON.parse(event.body);

        if (!email || !password) {
            return createResponse(400, { error: 'Email and password are required' });
        }

        // Normalize email to lowercase for case-insensitive handling
        const normalizedEmail = email.toLowerCase();

        if (!USER_POOL_ID || !USER_POOL_CLIENT_ID) {
            return createResponse(500, { error: 'User pool configuration missing' });
        }

        // Use SignUp command instead of AdminCreateUser to trigger email verification
        const signUpParams: SignUpCommandInput = {
            ClientId: USER_POOL_CLIENT_ID,
            Username: normalizedEmail,
            Password: password,
            UserAttributes: [
                { Name: 'email', Value: normalizedEmail },
                { Name: 'given_name', Value: firstName || '' },
                { Name: 'family_name', Value: lastName || '' }
            ]
        };

        const signUpCommand = new SignUpCommand(signUpParams);
        const signUpResult = await cognitoClient.send(signUpCommand);

        console.log('SignUp result:', signUpResult);

        // Check if email verification is required based on environment
        const isDevelopment = process.env.ENVIRONMENT === 'development';
        const requiresVerification = !isDevelopment;

        // Auto-confirm user in development environment
        if (isDevelopment) {
            try {
                const confirmCommand = new AdminConfirmSignUpCommand({
                    UserPoolId: USER_POOL_ID,
                    Username: normalizedEmail
                });
                await cognitoClient.send(confirmCommand);
                console.log('User auto-confirmed for development environment');
            } catch (confirmError) {
                console.error('Failed to auto-confirm user:', confirmError);
                // Continue anyway - the user might already be confirmed
            }
        }

        // The user is created but not confirmed yet - they need to verify their email (unless in development)
        // We'll create a temporary user record that will be updated after email verification
        const tempUserId = signUpResult.UserSub;
        if (!tempUserId) {
            return createResponse(500, { error: 'Failed to get user ID from Cognito' });
        }

        // Create user record in DynamoDB with unverified status
        const userRecord: UserRecord = {
            id: tempUserId,
            email: normalizedEmail,
            firstName: firstName || '',
            lastName: lastName || '',
            cognitoUserId: normalizedEmail, // Use email as Cognito username
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };

        if (!USERS_TABLE) {
            return createResponse(500, { error: 'Users table configuration missing' });
        }

        const putCommand = new PutCommand({
            TableName: USERS_TABLE,
            Item: userRecord
        });
        await dynamodb.send(putCommand);

        // Return different messages based on whether verification is required
        const message = requiresVerification
            ? 'User created successfully. Please check your email to verify your account before signing in.'
            : 'User created successfully';

        return createResponse(201, {
            message,
            requiresVerification,
            user: {
                id: tempUserId,
                email: normalizedEmail,
                firstName: firstName || '',
                lastName: lastName || ''
            }
        });

    } catch (error: any) {
        console.error('SignUp error:', error);

        // Handle specific Cognito errors
        if (error.name === 'UsernameExistsException') {
            return createResponse(409, {
                error: 'Email already registered',
                details: 'An account with this email already exists. Try logging in instead.'
            });
        } else if (error.name === 'InvalidPasswordException') {
            return createResponse(400, {
                error: 'Invalid password',
                details: 'Password must be at least 8 characters long and contain uppercase, lowercase, and numeric characters.'
            });
        } else if (error.name === 'InvalidParameterException') {
            if (error.message?.toLowerCase().includes('password')) {
                return createResponse(400, {
                    error: 'Invalid password',
                    details: 'Password must be at least 8 characters long and contain uppercase, lowercase, and numeric characters.'
                });
            } else if (error.message?.toLowerCase().includes('email')) {
                return createResponse(400, {
                    error: 'Invalid email',
                    details: 'Please enter a valid email address.'
                });
            } else {
                return createResponse(400, {
                    error: 'Invalid request',
                    details: 'Please check your input and try again.'
                });
            }
        } else if (error.name === 'TooManyRequestsException') {
            return createResponse(429, {
                error: 'Too many attempts',
                details: 'Too many sign-up attempts. Please try again later.'
            });
        } else {
            return createResponse(500, {
                error: 'Failed to create user',
                details: 'An unexpected error occurred. Please try again.'
            });
        }
    }
};
