import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { CognitoIdentityProviderClient, ForgotPasswordCommand } from '@aws-sdk/client-cognito-identity-provider';

const cognitoClient = new CognitoIdentityProviderClient({ region: process.env.AWS_REGION });

const USER_POOL_CLIENT_ID = process.env.USER_POOL_CLIENT_ID;

// Helper function to create response
const createResponse = (statusCode: number, body: any): APIGatewayProxyResult => ({
    statusCode,
    headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token',
        'Access-Control-Allow-Methods': 'OPTIONS,POST,GET'
    },
    body: JSON.stringify(body)
});

interface ForgotPasswordRequest {
    email: string;
}

// Forgot password handler
export const handler = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    console.log('ForgotPassword Event:', JSON.stringify(event, null, 2));

    // Handle CORS preflight requests
    if (event.httpMethod === 'OPTIONS') {
        return createResponse(200, {});
    }

    try {
        if (!event.body) {
            return createResponse(400, { error: 'Request body is required' });
        }

        const { email }: ForgotPasswordRequest = JSON.parse(event.body);

        if (!email) {
            return createResponse(400, { error: 'Email is required' });
        }

        // Normalize email to lowercase for case-insensitive handling
        const normalizedEmail = email.toLowerCase();

        if (!USER_POOL_CLIENT_ID) {
            return createResponse(500, { error: 'Cognito configuration missing' });
        }

        // Initiate forgot password flow
        const forgotPasswordCommand = new ForgotPasswordCommand({
            ClientId: USER_POOL_CLIENT_ID,
            Username: normalizedEmail,
        });

        await cognitoClient.send(forgotPasswordCommand);

        console.log(`Password reset email sent successfully for: ${normalizedEmail}`);

        return createResponse(200, {
            message: 'Password reset email sent successfully',
            details: 'Please check your email for the password reset code.'
        });

    } catch (error: any) {
        console.error('ForgotPassword Error:', error);

        // Handle specific Cognito errors
        if (error.name === 'UserNotFoundException') {
            return createResponse(404, {
                error: 'User not found',
                details: 'No account found with this email address.'
            });
        } else if (error.name === 'InvalidParameterException') {
            return createResponse(404, {
                error: 'User not found',
                details: 'No account found with this email address.'
            });
        } else if (error.name === 'LimitExceededException') {
            return createResponse(429, {
                error: 'Too many requests',
                details: 'Too many password reset emails sent. Please wait before requesting another.'
            });
        } else if (error.name === 'NotAuthorizedException') {
            return createResponse(400, {
                error: 'User not confirmed',
                details: 'Please verify your email address first before resetting your password.'
            });
        } else {
            return createResponse(500, {
                error: 'Password reset failed',
                details: 'An unexpected error occurred. Please try again.'
            });
        }
    }
};
