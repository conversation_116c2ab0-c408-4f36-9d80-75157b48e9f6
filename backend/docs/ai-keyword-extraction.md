# AI Keyword Extraction for Posts

This document describes the AI-powered keyword extraction system that enhances the TikTok-style recommendation algorithm.

## Overview

When users create posts with images, the system automatically extracts relevant keywords using AWS Rekognition AI analysis. These keywords are stored in the posts table and used by the engagement algorithm to provide better content recommendations.

## How It Works

### 1. Image Upload and AI Processing

When a user uploads an image:
1. **Image is uploaded** to S3 staging area
2. **AI processing Lambda** analyzes the image using AWS Rekognition
3. **Content analysis** extracts labels, categories, and suggested tags
4. **Results are stored** in the media table with AI analysis data

### 2. Post Creation with Keyword Extraction

When a post is created with media:
1. **Post creation** triggers keyword extraction from media AI analysis
2. **Keywords are filtered** to remove image properties, colors, and generic terms
3. **Relevant keywords** are stored in the posts table
4. **Engagement algorithm** uses keywords for better recommendations

### 3. Keyword-Enhanced Recommendations

The engagement algorithm now considers:
- **Base engagement metrics** (likes, comments, views, reactions)
- **Time decay** (recent content prioritized)
- **Keyword relevance** (content matching user interests)
- **User preferences** (learned from interaction patterns)

## Implementation Details

### Database Schema

**Posts Table - New Field:**
```typescript
interface Post {
  // ... existing fields
  keywords?: string[]; // AI-extracted keywords from media
}
```

### Keyword Extraction Process

```typescript
// Extract keywords from media AI analysis
const extractKeywordsFromMedia = async (mediaId: string): Promise<string[]> => {
  // 1. Get media record with AI analysis
  // 2. Extract suggestedTags from content_analysis
  // 3. Extract high-confidence labels (>80% confidence)
  // 4. Filter out image properties, colors, generic terms
  // 5. Return up to 15 relevant keywords
}
```

### Filtering Logic

**Excluded Terms:**
- **Image Properties**: brightness, contrast, sharpness, quality, etc.
- **Colors**: red, blue, green, yellow, etc.
- **Generic Terms**: image, photo, picture, content, etc.

**Included Terms:**
- **High-confidence labels** (>80% confidence from Rekognition)
- **Suggested tags** from AI analysis
- **Gaming-related keywords** (prioritized)
- **Specific objects and concepts**

### Integration Points

**1. Post Creation (`createPost`)**
```typescript
// Extract keywords when creating post with media
if (finalMediaId) {
  keywords = await extractKeywordsFromMedia(finalMediaId);
}
```

**2. Post Publishing (`publishPost`)**
```typescript
// Extract keywords when publishing draft with media
if (mediaId) {
  keywords = await extractKeywordsFromMedia(mediaId);
}
```

**3. Engagement Algorithm**
```typescript
// Apply keyword bonus in engagement scoring
const keywordBonus = post.keywords?.length * 2 || 0;
const finalScore = baseEngagementScore + keywordBonus;
```

## API Integration

### Post Response Format

Posts now include keywords in API responses:

```json
{
  "id": "post-123",
  "content": "Check out this amazing game!",
  "mediaId": "media-456",
  "keywords": [
    "gaming",
    "controller",
    "console",
    "action",
    "adventure"
  ],
  "engagementScore": 85.5,
  "keywordBonus": 10
}
```

### Engagement Feed Enhancement

The `/posts/engagement-feed` endpoint now:
- **Considers keyword relevance** for personalization
- **Applies keyword bonuses** to engagement scores
- **Returns keywordBonus** field showing keyword impact

## Example Workflow

### 1. User Uploads Gaming Screenshot

```
Image: Call of Duty gameplay screenshot
↓
AI Analysis: Detects "gaming", "shooter", "weapon", "action", "multiplayer"
↓
Keyword Extraction: ["gaming", "shooter", "action", "multiplayer"]
↓
Post Creation: Keywords stored in posts table
```

### 2. Recommendation Algorithm

```
User A likes gaming content
↓
Post with keywords ["gaming", "shooter"] gets keyword bonus
↓
Higher engagement score = better ranking in feed
↓
User A sees more relevant gaming content
```

## Benefits

### 1. **Better Content Discovery**
- Users find content matching their interests
- Gaming posts reach gaming enthusiasts
- Niche content finds its audience

### 2. **Improved Engagement**
- More relevant recommendations = higher engagement
- Users spend more time on platform
- Better user satisfaction

### 3. **Automatic Categorization**
- No manual tagging required
- Consistent keyword extraction
- Scales with content volume

### 4. **Enhanced Personalization**
- Algorithm learns user preferences
- Content recommendations improve over time
- Better user retention

## Monitoring and Analytics

### Key Metrics to Track

1. **Keyword Quality**
   - Relevance of extracted keywords
   - User engagement with keyword-tagged content
   - Keyword diversity across posts

2. **Algorithm Performance**
   - Engagement rates for keyword-enhanced posts
   - User session duration improvements
   - Content discovery metrics

3. **AI Accuracy**
   - Precision of keyword extraction
   - Gaming content detection rates
   - False positive/negative rates

### New Relic Events

The system logs these events for monitoring:
- `keyword_extraction_completed` - When keywords are extracted
- `keyword_bonus_applied` - When keyword bonus affects ranking
- `ai_analysis_processed` - When AI analysis completes

## Configuration

### Environment Variables

```bash
# Existing tables
POSTS_TABLE=gameflex-staging-Posts
MEDIA_TABLE=gameflex-staging-Media

# New engagement tables
POST_ENGAGEMENT_METRICS_TABLE=gameflex-staging-PostEngagementMetrics
USER_PREFERENCES_TABLE=gameflex-staging-UserPreferences
```

### AI Processing Settings

```typescript
// Keyword extraction thresholds
const MIN_CONFIDENCE = 80; // Minimum confidence for labels
const MAX_KEYWORDS = 15;   // Maximum keywords per post
const MIN_KEYWORD_LENGTH = 3; // Minimum keyword length
```

## Testing

### Manual Testing

1. **Upload gaming image** to test AI keyword extraction
2. **Create post** with the image
3. **Verify keywords** appear in post response
4. **Check engagement feed** for keyword bonuses

### Automated Testing

```bash
# Run keyword extraction tests
npm test -- keyword-extraction.test.ts

# Test engagement algorithm with keywords
npm test -- engagement-algorithm.test.ts
```

## Future Enhancements

### 1. **Advanced AI Models**
- Use CLIP for better gaming content detection
- Custom trained models for gaming keywords
- Multi-modal analysis (text + image)

### 2. **User Feedback Integration**
- Allow users to confirm/correct keywords
- Learn from user interactions with keywords
- Improve keyword quality over time

### 3. **Semantic Matching**
- Use embeddings for keyword similarity
- Match related concepts (e.g., "FPS" and "shooter")
- Better personalization through semantic understanding

### 4. **Real-time Updates**
- Update keywords when AI analysis improves
- Re-process old content with better models
- Dynamic keyword relevance scoring

## Troubleshooting

### Common Issues

1. **No keywords extracted**
   - Check if media has AI analysis completed
   - Verify image quality and content
   - Check confidence thresholds

2. **Poor keyword quality**
   - Review filtering logic
   - Adjust confidence thresholds
   - Check AI model performance

3. **Low keyword bonuses**
   - Verify user preference learning
   - Check keyword matching logic
   - Monitor engagement patterns

### Debug Commands

```bash
# Check media AI analysis
aws dynamodb get-item --table-name gameflex-staging-Media --key '{"id":{"S":"media-id"}}'

# Check post keywords
aws dynamodb get-item --table-name gameflex-staging-Posts --key '{"id":{"S":"post-id"}}'

# Monitor keyword extraction logs
aws logs filter-log-events --log-group-name /aws/lambda/posts-function
```

## Summary

The AI keyword extraction system enhances the TikTok-style recommendation algorithm by:

✅ **Automatically extracting** relevant keywords from images
✅ **Filtering out** irrelevant terms (colors, properties, generic words)
✅ **Storing keywords** in posts for recommendation algorithm
✅ **Applying keyword bonuses** in engagement scoring
✅ **Improving content discovery** and personalization
✅ **Scaling automatically** with content volume

This creates a more intelligent recommendation system that understands content context and user preferences, leading to better engagement and user satisfaction.
