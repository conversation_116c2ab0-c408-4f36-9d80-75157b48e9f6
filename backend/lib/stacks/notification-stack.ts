import * as cdk from 'aws-cdk-lib';
import * as sns from 'aws-cdk-lib/aws-sns';
import * as dynamodb from 'aws-cdk-lib/aws-dynamodb';
import * as lambda from 'aws-cdk-lib/aws-lambda';
import * as iam from 'aws-cdk-lib/aws-iam';
import { Construct } from 'constructs';

export interface NotificationStackProps extends cdk.NestedStackProps {
  projectName: string;
  environment: string;
  isProductionOrStaging: boolean;
}

export interface NotificationStackOutputs {
  notificationTopic: sns.Topic;
  notificationFunction: lambda.Function;
  tables: {
    notifications: dynamodb.Table;
    deviceTokens: dynamodb.Table;
    notificationPreferences: dynamodb.Table;
    notificationHistory: dynamodb.Table;
  };
}

export class NotificationStack extends cdk.NestedStack implements NotificationStackOutputs {
  public readonly notificationTopic: sns.Topic;
  public readonly notificationFunction: lambda.Function;
  public readonly tables: {
    notifications: dynamodb.Table;
    deviceTokens: dynamodb.Table;
    notificationPreferences: dynamodb.Table;
    notificationHistory: dynamodb.Table;
  };

  constructor(scope: Construct, id: string, props: NotificationStackProps) {
    super(scope, id, props);

    const { projectName, environment, isProductionOrStaging } = props;

    // Create DynamoDB tables for notifications
    this.tables = this.createNotificationTables(projectName, environment, isProductionOrStaging);

    // Create SNS topic for push notifications
    this.notificationTopic = this.createNotificationTopic(projectName, environment);

    // Create notification processing Lambda function
    this.notificationFunction = this.createNotificationFunction(projectName, environment);

    // Grant permissions
    this.grantPermissions();

    // Create outputs
    this.createOutputs();
  }

  private createNotificationTables(
    projectName: string,
    environment: string,
    isProductionOrStaging: boolean
  ): {
    notifications: dynamodb.Table;
    deviceTokens: dynamodb.Table;
    notificationPreferences: dynamodb.Table;
    notificationHistory: dynamodb.Table;
  } {
    const tableConfig = {
      billingMode: dynamodb.BillingMode.PAY_PER_REQUEST,
      deletionProtection: isProductionOrStaging,
      pointInTimeRecoverySpecification: {
        pointInTimeRecoveryEnabled: isProductionOrStaging,
      },
      removalPolicy: isProductionOrStaging ? cdk.RemovalPolicy.RETAIN : cdk.RemovalPolicy.DESTROY,
    };

    // Notifications Table - stores notification records
    const notifications = new dynamodb.Table(this, 'NotificationsTable', {
      ...tableConfig,
      tableName: `${projectName}-${environment}-Notifications`,
      partitionKey: { name: 'id', type: dynamodb.AttributeType.STRING },
    });

    // Add GSI for user notifications
    notifications.addGlobalSecondaryIndex({
      indexName: 'userId-createdAt-index',
      partitionKey: { name: 'userId', type: dynamodb.AttributeType.STRING },
      sortKey: { name: 'createdAt', type: dynamodb.AttributeType.STRING },
    });

    // Add GSI for notification type queries
    notifications.addGlobalSecondaryIndex({
      indexName: 'type-createdAt-index',
      partitionKey: { name: 'type', type: dynamodb.AttributeType.STRING },
      sortKey: { name: 'createdAt', type: dynamodb.AttributeType.STRING },
    });

    // Device Tokens Table - stores FCM/APNS tokens
    const deviceTokens = new dynamodb.Table(this, 'DeviceTokensTable', {
      ...tableConfig,
      tableName: `${projectName}-${environment}-DeviceTokens`,
      partitionKey: { name: 'userId', type: dynamodb.AttributeType.STRING },
      sortKey: { name: 'deviceId', type: dynamodb.AttributeType.STRING },
    });

    // Add GSI for token lookup
    deviceTokens.addGlobalSecondaryIndex({
      indexName: 'token-index',
      partitionKey: { name: 'token', type: dynamodb.AttributeType.STRING },
    });

    // Notification Preferences Table - user notification settings
    const notificationPreferences = new dynamodb.Table(this, 'NotificationPreferencesTable', {
      ...tableConfig,
      tableName: `${projectName}-${environment}-NotificationPreferences`,
      partitionKey: { name: 'userId', type: dynamodb.AttributeType.STRING },
    });

    // Notification History Table - tracks sent notifications for rate limiting
    const notificationHistory = new dynamodb.Table(this, 'NotificationHistoryTable', {
      ...tableConfig,
      tableName: `${projectName}-${environment}-NotificationHistory`,
      partitionKey: { name: 'userId', type: dynamodb.AttributeType.STRING },
      sortKey: { name: 'timestamp', type: dynamodb.AttributeType.STRING },
      // TTL for automatic cleanup of old history records (30 days)
      timeToLiveAttribute: 'ttl',
    });

    // Add GSI for notification type rate limiting
    notificationHistory.addGlobalSecondaryIndex({
      indexName: 'userId-type-index',
      partitionKey: { name: 'userId', type: dynamodb.AttributeType.STRING },
      sortKey: { name: 'type', type: dynamodb.AttributeType.STRING },
    });

    return {
      notifications,
      deviceTokens,
      notificationPreferences,
      notificationHistory,
    };
  }

  private createNotificationTopic(projectName: string, environment: string): sns.Topic {
    return new sns.Topic(this, 'NotificationTopic', {
      topicName: `${projectName}-notifications-${environment}`,
      displayName: `GameFlex Notifications - ${environment}`,
      // Enable message filtering
      fifo: false,
    });
  }

  private createNotificationFunction(projectName: string, environment: string): lambda.Function {
    const functionName = `${projectName}-notifications-${environment}`;

    return new lambda.Function(this, 'NotificationFunction', {
      functionName,
      runtime: lambda.Runtime.NODEJS_20_X,
      handler: 'index.handler',
      code: lambda.Code.fromAsset('src/notifications'),
      timeout: cdk.Duration.minutes(2),
      memorySize: 512,
      environment: {
        ENVIRONMENT: environment,
        NOTIFICATIONS_TABLE: this.tables.notifications.tableName,
        DEVICE_TOKENS_TABLE: this.tables.deviceTokens.tableName,
        NOTIFICATION_PREFERENCES_TABLE: this.tables.notificationPreferences.tableName,
        NOTIFICATION_HISTORY_TABLE: this.tables.notificationHistory.tableName,
        SNS_TOPIC_ARN: this.notificationTopic.topicArn,
      },
    });
  }

  private grantPermissions(): void {
    // Grant Lambda function permissions to access DynamoDB tables
    this.tables.notifications.grantReadWriteData(this.notificationFunction);
    this.tables.deviceTokens.grantReadData(this.notificationFunction);
    this.tables.notificationPreferences.grantReadData(this.notificationFunction);
    this.tables.notificationHistory.grantReadWriteData(this.notificationFunction);

    // Grant SNS permissions
    this.notificationTopic.grantPublish(this.notificationFunction);

    // Grant mobile push notification permissions
    this.notificationFunction.addToRolePolicy(
      new iam.PolicyStatement({
        effect: iam.Effect.ALLOW,
        actions: [
          'sns:CreatePlatformEndpoint',
          'sns:DeleteEndpoint',
          'sns:GetEndpointAttributes',
          'sns:SetEndpointAttributes',
          'sns:Publish',
        ],
        resources: ['*'],
      })
    );
  }

  private createOutputs(): void {
    new cdk.CfnOutput(this, 'NotificationTopicArn', {
      value: this.notificationTopic.topicArn,
      description: 'ARN of the notification SNS topic',
    });

    new cdk.CfnOutput(this, 'NotificationFunctionArn', {
      value: this.notificationFunction.functionArn,
      description: 'ARN of the notification processing function',
    });

    // Output table names for other stacks to reference
    Object.entries(this.tables).forEach(([key, table]) => {
      new cdk.CfnOutput(this, `${key}TableName`, {
        value: table.tableName,
        description: `Name of the ${key} table`,
      });
    });
  }
}
