import * as cdk from 'aws-cdk-lib';
import * as cognito from 'aws-cdk-lib/aws-cognito';
import * as lambda from 'aws-cdk-lib/aws-lambda';
import * as iam from 'aws-cdk-lib/aws-iam';
import { Construct } from 'constructs';

export interface AuthenticationStackProps extends cdk.NestedStackProps {
  projectName: string;
  environment: string;
  isProductionOrStaging: boolean;
}

export interface AuthenticationStackOutputs {
  userPool: cognito.UserPool;
  userPoolClient: cognito.UserPoolClient;
}

export class AuthenticationStack extends cdk.NestedStack implements AuthenticationStackOutputs {
  public readonly userPool: cognito.UserPool;
  public readonly userPoolClient: cognito.UserPoolClient;

  constructor(scope: Construct, id: string, props: AuthenticationStackProps) {
    super(scope, id, props);

    const { projectName, environment, isProductionOrStaging } = props;

    // Create Lambda function for custom message trigger
    const customMessageFunction = new lambda.Function(this, 'CustomMessageFunction', {
      functionName: `${projectName}-custom-message-${environment}`,
      runtime: lambda.Runtime.NODEJS_22_X,
      handler: 'custom-message.handler',
      code: lambda.Code.fromAsset('src/cognito-triggers'),
      timeout: cdk.Duration.seconds(30),
      environment: {
        ENVIRONMENT: environment,
      },

    });

    // Grant SES permissions to the custom message function
    const sesIdentityArn = `arn:aws:ses:${cdk.Stack.of(this).region}:${cdk.Stack.of(this).account}:identity/gameflex.io`;
    customMessageFunction.addToRolePolicy(new iam.PolicyStatement({
      effect: iam.Effect.ALLOW,
      actions: [
        'ses:SendEmail',
        'ses:SendRawEmail',
      ],
      resources: [
        sesIdentityArn,
        `arn:aws:ses:${cdk.Stack.of(this).region}:${cdk.Stack.of(this).account}:identity/gameflex.io`,
      ],
    }));



    // Cognito User Pool
    this.userPool = new cognito.UserPool(this, 'UserPool', {
      userPoolName: `${projectName}-users-${environment}`,
      autoVerify: environment === 'development' ? {} : { email: true },
      signInAliases: { email: true },
      passwordPolicy: {
        minLength: 8,
        requireUppercase: true,
        requireLowercase: true,
        requireDigits: true,
        requireSymbols: false,
      },
      deletionProtection: isProductionOrStaging,
      removalPolicy: isProductionOrStaging ? cdk.RemovalPolicy.RETAIN : cdk.RemovalPolicy.DESTROY,
      // Custom message templates for password reset
      accountRecovery: cognito.AccountRecovery.EMAIL_ONLY,
      // Lambda triggers
      lambdaTriggers: {
        customMessage: customMessageFunction,
      },
      // Email verification configuration - removed to use custom message Lambda instead
      // userVerification: {
      //   emailSubject: 'Verify your GameFlex account',
      //   emailBody: 'Welcome to GameFlex! Please verify your account with this code: {####}',
      //   emailStyle: cognito.VerificationEmailStyle.CODE,
      // },
      // Allow self-registration (this is the key setting!)
      selfSignUpEnabled: true,
    });

    // Add custom message configuration for password reset
    const cfnUserPool = this.userPool.node.defaultChild as cognito.CfnUserPool;

    // Configure SES for all environments (development, staging, and production)
    // This ensures custom email templates and sender address are used consistently
    cfnUserPool.emailConfiguration = {
      emailSendingAccount: 'DEVELOPER',
      from: 'GameFlex <<EMAIL>>',
      replyToEmailAddress: '<EMAIL>',
      sourceArn: sesIdentityArn,
    };

    // Cognito User Pool Client
    this.userPoolClient = new cognito.UserPoolClient(this, 'UserPoolClient', {
      userPool: this.userPool,
      userPoolClientName: `${projectName}-client-${environment}`,
      generateSecret: false,
      authFlows: {
        adminUserPassword: true,
        userPassword: true,
        userSrp: true,
      },
      refreshTokenValidity: cdk.Duration.days(30),
      accessTokenValidity: cdk.Duration.minutes(60),
      idTokenValidity: cdk.Duration.minutes(60),
      // Prevent unverified users from signing in for staging and production
      preventUserExistenceErrors: true,
    });

    // Configure email verification requirements for staging and production
    if (isProductionOrStaging) {
      const cfnUserPoolClient = this.userPoolClient.node.defaultChild as cognito.CfnUserPoolClient;
      cfnUserPoolClient.addPropertyOverride('ExplicitAuthFlows', [
        'ALLOW_USER_SRP_AUTH',
        'ALLOW_USER_PASSWORD_AUTH',
        'ALLOW_ADMIN_USER_PASSWORD_AUTH',
        'ALLOW_REFRESH_TOKEN_AUTH'
      ]);

      // Add user pool policy to require email verification
      cfnUserPool.addPropertyOverride('Policies', {
        PasswordPolicy: {
          MinimumLength: 8,
          RequireUppercase: true,
          RequireLowercase: true,
          RequireNumbers: true,
          RequireSymbols: false,
        }
      });

      cfnUserPool.addPropertyOverride('UserAttributeUpdateSettings', {
        AttributesRequireVerificationBeforeUpdate: ['email']
      });
    }

    // Create outputs
    this.createOutputs();
  }

  private createOutputs(): void {
    // User Pool ID
    new cdk.CfnOutput(this, 'UserPoolId', {
      value: this.userPool.userPoolId,
      description: 'Cognito User Pool ID',
      exportName: `${this.stackName}-UserPoolId`,
    });

    // User Pool ARN
    new cdk.CfnOutput(this, 'UserPoolArn', {
      value: this.userPool.userPoolArn,
      description: 'Cognito User Pool ARN',
      exportName: `${this.stackName}-UserPoolArn`,
    });

    // User Pool Client ID
    new cdk.CfnOutput(this, 'UserPoolClientId', {
      value: this.userPoolClient.userPoolClientId,
      description: 'Cognito User Pool Client ID',
      exportName: `${this.stackName}-UserPoolClientId`,
    });
  }
}
