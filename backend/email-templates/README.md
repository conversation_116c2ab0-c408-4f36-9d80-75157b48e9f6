# GameFlex Email Templates

This directory contains the email templates used by Amazon Cognito for user authentication flows.

## Templates

### Verification Email (`verification-email.html` / `verification-email.txt`)
- **Purpose**: Sent when users sign up or request email verification
- **Trigger**: User registration, email verification requests
- **Variables**: `{####}` - Verification code

### Password Reset Email (`password-reset-email.html` / `password-reset-email.txt`)
- **Purpose**: Sent when users request password reset
- **Trigger**: Forgot password flow
- **Variables**: `{####}` - Password reset code

## Configuration

### Email Settings
- **From Address**: `<EMAIL>`
- **From Name**: `GameFlex`
- **Reply-To**: `<EMAIL>`

### SES Requirements
The following SES identity must be verified in your AWS account:
- Domain: `gameflex.io`
- Email: `<EMAIL>`

### Cognito Integration
The templates are integrated with Cognito through:
1. **Lambda Trigger**: Custom message trigger function (`src/cognito-triggers/custom-message.ts`)
2. **User Pool Configuration**: Email settings in `lib/stacks/authentication-stack.ts`

## Template Variables

Cognito automatically replaces the following variables in email templates:
- `{####}` - Verification or reset code
- `{username}` - User's username (if applicable)

## Styling Guidelines

### HTML Templates
- Use inline CSS for maximum email client compatibility
- Responsive design with mobile-first approach
- GameFlex branding colors:
  - Primary: `#6366f1` (Indigo)
  - Text: `#1f2937` (Gray-800)
  - Background: `#f8f9fa` (Light gray)

### Text Templates
- Plain text fallback for HTML emails
- Clear, concise messaging
- Include all essential information from HTML version

## Testing

To test email templates:
1. Deploy the stack with updated templates
2. Trigger the authentication flow (signup, password reset)
3. Check email delivery and formatting

## Customization

To modify templates:
1. Edit the HTML/text files in this directory
2. Redeploy the authentication stack
3. Test the changes in your environment

## Security Notes

- Templates are read at deployment time and embedded in Lambda functions
- No sensitive information should be included in templates
- All email communications use encrypted channels (SES)
- Verification codes expire automatically (24 hours for verification, 1 hour for password reset)
