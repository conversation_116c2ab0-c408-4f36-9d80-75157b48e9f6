import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../theme/app_theme.dart';
import '../components/index.dart';
import '../providers/auth_provider.dart';
import '../providers/user_profile_provider.dart';
import '../models/linked_account_model.dart';
import 'liked_posts_screen.dart';
import 'edit_profile_screen.dart';
import 'user_profile_screen.dart';
import 'account_linking_screen.dart';

class ProfileScreen extends StatefulWidget {
  const ProfileScreen({super.key});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  late UserProfileProvider _profileProvider;

  @override
  void initState() {
    super.initState();
    _profileProvider = UserProfileProvider();
    _loadCurrentUserProfile();
  }

  @override
  void dispose() {
    _profileProvider.dispose();
    super.dispose();
  }

  Future<void> _loadCurrentUserProfile() async {
    if (!mounted) return;

    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final currentUser = authProvider.user;

    if (currentUser != null && mounted) {
      await _profileProvider.loadUserProfile(currentUser.id, loadPosts: false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider.value(
      value: _profileProvider,
      child: Consumer2<AuthProvider, UserProfileProvider>(
        builder: (context, authProvider, profileProvider, child) {
          final user = authProvider.user;
          final profileData = profileProvider.userProfile;

          return Scaffold(
            body: SafeArea(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(24.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    const SizedBox(height: 20),

                    // Profile Avatar
                    Container(
                      width: 120,
                      height: 120,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        gradient: const LinearGradient(
                          colors: [AppColors.gfLightTeal, AppColors.gfTeal],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                        border: Border.all(color: AppColors.gfGreen, width: 3),
                      ),
                      child: const Icon(
                        Icons.person,
                        size: 60,
                        color: AppColors.gfDarkBlue,
                      ),
                    ),

                    const SizedBox(height: 24),

                    // User Display Name (Username or Display Name)
                    Text(
                      _getDisplayName(user, profileData),
                      style: const TextStyle(
                        fontSize: 28,
                        fontWeight: FontWeight.bold,
                        color: AppColors.gfOffWhite,
                      ),
                    ),

                    const SizedBox(height: 8),

                    // User Email or Linked Account Info
                    _buildUserIdentifier(user, profileData),

                    // Linked Accounts
                    if (profileData != null &&
                        _hasLinkedAccounts(profileData)) ...[
                      const SizedBox(height: 16),
                      _buildLinkedAccountsSection(profileData),
                    ],

                    const SizedBox(height: 32),

                    // Profile Stats
                    Container(
                      padding: const EdgeInsets.all(20),
                      decoration: BoxDecoration(
                        color: AppColors.gfDarkBackground,
                        borderRadius: BorderRadius.circular(16),
                        border: Border.all(
                          color: AppColors.gfGreen.withValues(
                            alpha: 77,
                          ), // 0.3 opacity
                          width: 1,
                        ),
                      ),
                      child: Column(
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceAround,
                            children: [
                              _buildStatItem('Posts', '0'),
                              _buildStatItem('Followers', '0'),
                              _buildStatItem('Following', '0'),
                            ],
                          ),
                          const SizedBox(height: 20),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceAround,
                            children: [
                              _buildStatItem('Likes', '0'),
                              _buildStatItem('Comments', '0'),
                              _buildStatItem('Shares', '0'),
                            ],
                          ),
                        ],
                      ),
                    ),

                    const SizedBox(height: 32),

                    // Account Information
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(20),
                      decoration: BoxDecoration(
                        color: AppColors.gfDarkBackground,
                        borderRadius: BorderRadius.circular(16),
                        border: Border.all(
                          color: AppColors.gfGreen.withValues(
                            alpha: 77,
                          ), // 0.3 opacity
                          width: 1,
                        ),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'Account Information',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: AppColors.gfOffWhite,
                            ),
                          ),
                          const SizedBox(height: 16),
                          _buildInfoRow('User ID', user?.id ?? 'N/A'),
                          const SizedBox(height: 12),
                          _buildInfoRow(
                            'Email Verified',
                            user?.isEmailConfirmed == true ? 'Yes' : 'No',
                          ),
                          const SizedBox(height: 12),
                          _buildInfoRow(
                            'Member Since',
                            _formatDate(user?.createdAt),
                          ),
                          const SizedBox(height: 12),
                          _buildInfoRow(
                            'Last Updated',
                            _formatDate(user?.updatedAt),
                          ),
                        ],
                      ),
                    ),

                    const SizedBox(height: 40),

                    // Action Buttons
                    Column(
                      children: [
                        // My Profile Button
                        SizedBox(
                          width: double.infinity,
                          child: GFButton(
                            text: 'My Profile',
                            onPressed: () {
                              if (user?.id != null) {
                                Navigator.of(context).push(
                                  MaterialPageRoute(
                                    builder:
                                        (context) => UserProfileScreen(
                                          userId: user!.id,
                                          username: user.email.split('@').first,
                                        ),
                                  ),
                                );
                              }
                            },
                            type: GFButtonType.primary,
                          ),
                        ),

                        const SizedBox(height: 16),

                        // Edit Profile Button
                        SizedBox(
                          width: double.infinity,
                          child: GFButton(
                            text: 'Edit Profile',
                            onPressed: () {
                              Navigator.of(context).push(
                                MaterialPageRoute(
                                  builder:
                                      (context) => const EditProfileScreen(),
                                ),
                              );
                            },
                            type: GFButtonType.secondary,
                          ),
                        ),

                        const SizedBox(height: 16),

                        // Account Linking Button
                        SizedBox(
                          width: double.infinity,
                          child: GFButton(
                            text: 'Link Accounts',
                            onPressed: () async {
                              await Navigator.of(context).push(
                                MaterialPageRoute(
                                  builder:
                                      (context) => const AccountLinkingScreen(),
                                ),
                              );
                              // Refresh profile data when returning from account linking
                              if (mounted) {
                                await _loadCurrentUserProfile();
                              }
                            },
                            type: GFButtonType.secondary,
                          ),
                        ),

                        const SizedBox(height: 16),

                        // Liked Posts Button
                        SizedBox(
                          width: double.infinity,
                          child: GFButton(
                            text: 'Liked Posts',
                            onPressed: () {
                              Navigator.of(context).push(
                                MaterialPageRoute(
                                  builder:
                                      (context) => const LikedPostsScreen(),
                                ),
                              );
                            },
                            type: GFButtonType.secondary,
                          ),
                        ),

                        const SizedBox(height: 16),

                        // Settings Button
                        SizedBox(
                          width: double.infinity,
                          child: GFButton(
                            text: 'Settings',
                            onPressed: () {
                              // TODO: Navigate to settings screen
                              ScaffoldMessenger.of(context).showSnackBar(
                                const SnackBar(
                                  content: Text(
                                    'Settings feature coming soon!',
                                  ),
                                  backgroundColor: AppColors.gfTeal,
                                ),
                              );
                            },
                            type: GFButtonType.secondary,
                          ),
                        ),

                        const SizedBox(height: 24),

                        // Logout Button
                        SizedBox(
                          width: double.infinity,
                          child: GFButton(
                            text: 'Log Out',
                            onPressed:
                                () => _showLogoutDialog(context, authProvider),
                            type: GFButtonType.danger,
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 20),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildStatItem(String label, String value) {
    return Column(
      children: [
        Text(
          value,
          style: const TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: AppColors.gfGreen,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: const TextStyle(fontSize: 12, color: AppColors.gfGrayText),
        ),
      ],
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 120,
          child: Text(
            label,
            style: const TextStyle(fontSize: 14, color: AppColors.gfGrayText),
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: const TextStyle(fontSize: 14, color: AppColors.gfOffWhite),
          ),
        ),
      ],
    );
  }

  String _formatDate(DateTime? date) {
    if (date == null) return 'N/A';
    return '${date.day}/${date.month}/${date.year}';
  }

  String _getDisplayName(dynamic user, Map<String, dynamic>? profileData) {
    // If we have profile data, use the username from there
    if (profileData != null &&
        profileData['username'] != null &&
        profileData['username'].toString().isNotEmpty) {
      return profileData['username'].toString();
    }

    // Fall back to user display name or username
    if (user?.username != null && user!.username!.isNotEmpty) {
      return user.username!;
    }

    return user?.displayName ?? 'User';
  }

  Widget _buildUserIdentifier(dynamic user, Map<String, dynamic>? profileData) {
    // Check if this is a temp email (Xbox account)
    final email = user?.email ?? '';
    final isXboxTempEmail = email.contains('@gameflex.temp');

    if (isXboxTempEmail &&
        profileData != null &&
        _hasLinkedAccounts(profileData)) {
      // For Xbox accounts, show "Signed in with Xbox" instead of temp email
      final linkedAccounts = _getLinkedAccountsList(profileData);
      final xboxAccount = linkedAccounts.firstWhere(
        (account) => account.type == LinkedAccountType.xbox,
        orElse: () => linkedAccounts.first,
      );

      return Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            'Signed in with ${xboxAccount.type.displayName}',
            style: const TextStyle(fontSize: 16, color: AppColors.gfGrayText),
          ),
        ],
      );
    } else {
      // For regular accounts, show email
      return Text(
        email.isNotEmpty ? email : 'No email',
        style: const TextStyle(fontSize: 16, color: AppColors.gfGrayText),
      );
    }
  }

  bool _hasLinkedAccounts(Map<String, dynamic> profileData) {
    final linkedAccounts =
        profileData['linkedAccounts'] as List<dynamic>? ?? [];
    return linkedAccounts.isNotEmpty;
  }

  List<LinkedAccountModel> _getLinkedAccountsList(
    Map<String, dynamic> profileData,
  ) {
    final linkedAccountsJson =
        profileData['linkedAccounts'] as List<dynamic>? ?? [];
    return linkedAccountsJson
        .map(
          (accountJson) =>
              LinkedAccountModel.fromJson(accountJson as Map<String, dynamic>),
        )
        .toList();
  }

  Widget _buildLinkedAccountsSection(Map<String, dynamic> profileData) {
    final linkedAccounts = _getLinkedAccountsList(profileData);

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.gfCardBackground,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppColors.gfGreen.withAlpha(77), // 0.3 opacity
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Linked Accounts',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: AppColors.gfOffWhite,
            ),
          ),
          const SizedBox(height: 12),
          ...linkedAccounts.map(
            (account) => Padding(
              padding: const EdgeInsets.only(bottom: 8),
              child: GFLinkedAccountWidget(
                account: account,
                showUnlinkButton: false,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showLogoutDialog(BuildContext context, AuthProvider authProvider) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: AppColors.gfDarkBackground,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: const Text(
            'Log Out',
            style: TextStyle(
              color: AppColors.gfOffWhite,
              fontWeight: FontWeight.bold,
            ),
          ),
          content: const Text(
            'Are you sure you want to log out?',
            style: TextStyle(color: AppColors.gfGrayText),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text(
                'Cancel',
                style: TextStyle(color: AppColors.gfGrayText),
              ),
            ),
            TextButton(
              onPressed: () async {
                Navigator.of(context).pop();
                await authProvider.signOut();
              },
              child: const Text(
                'Log Out',
                style: TextStyle(color: AppColors.gfGreen),
              ),
            ),
          ],
        );
      },
    );
  }
}
