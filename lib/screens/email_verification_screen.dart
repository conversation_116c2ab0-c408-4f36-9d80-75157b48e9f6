import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'dart:async';

import '../providers/auth_provider.dart';
import '../services/aws_auth_service.dart';
import '../theme/app_theme.dart';

class EmailVerificationScreen extends StatefulWidget {
  final String email;

  const EmailVerificationScreen({super.key, required this.email});

  @override
  State<EmailVerificationScreen> createState() =>
      _EmailVerificationScreenState();
}

class _EmailVerificationScreenState extends State<EmailVerificationScreen> {
  final List<TextEditingController> _codeControllers = List.generate(
    6,
    (index) => TextEditingController(),
  );
  final List<FocusNode> _focusNodes = List.generate(6, (index) => FocusNode());

  bool _isVerifying = false;
  bool _isResending = false;
  int _resendCooldown = 0;
  Timer? _cooldownTimer;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    // Auto-focus first input
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _focusNodes[0].requestFocus();
    });
  }

  @override
  void dispose() {
    for (var controller in _codeControllers) {
      controller.dispose();
    }
    for (var focusNode in _focusNodes) {
      focusNode.dispose();
    }
    _cooldownTimer?.cancel();
    super.dispose();
  }

  String get _verificationCode {
    return _codeControllers.map((controller) => controller.text).join();
  }

  bool get _isCodeComplete {
    return _verificationCode.length == 6;
  }

  void _onCodeChanged(int index, String value) {
    if (value.isNotEmpty && index < 5) {
      // Move to next field
      _focusNodes[index + 1].requestFocus();
    } else if (value.isEmpty && index > 0) {
      // Move to previous field
      _focusNodes[index - 1].requestFocus();
    }

    // Clear error when user starts typing
    if (_errorMessage != null) {
      setState(() {
        _errorMessage = null;
      });
    }

    // Auto-verify when code is complete
    if (_isCodeComplete && !_isVerifying) {
      // Add a small delay to allow user to see the complete code
      Future.delayed(const Duration(milliseconds: 300), () {
        if (mounted && _isCodeComplete && !_isVerifying) {
          _verifyCode();
        }
      });
    }
  }

  void _onCodePasted(String pastedText) {
    // Handle pasted verification codes
    final cleanedText = pastedText.replaceAll(RegExp(r'[^0-9]'), '');
    if (cleanedText.length <= 6) {
      for (int i = 0; i < 6; i++) {
        _codeControllers[i].text = i < cleanedText.length ? cleanedText[i] : '';
      }
      if (cleanedText.length == 6) {
        _verifyCode();
      }
    }
  }

  Future<void> _verifyCode() async {
    if (!_isCodeComplete || _isVerifying) return;

    setState(() {
      _isVerifying = true;
      _errorMessage = null;
    });

    try {
      final authService = AwsAuthService.instance;
      final result = await authService.confirmSignup(
        widget.email,
        _verificationCode,
      );

      if (result['success'] == true) {
        if (mounted) {
          // Show success message
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  const Icon(Icons.check_circle, color: Colors.white),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      result['message'] ?? 'Email verified successfully!',
                    ),
                  ),
                ],
              ),
              backgroundColor: AppColors.gfGreen,
              duration: const Duration(seconds: 3),
            ),
          );

          // Update auth provider status
          final authProvider = Provider.of<AuthProvider>(
            context,
            listen: false,
          );
          authProvider.onEmailVerified();

          // Navigate back or to next screen after a short delay
          Future.delayed(const Duration(seconds: 1), () {
            if (mounted) {
              Navigator.of(context).pop(true); // Return success result
            }
          });
        }
      } else {
        setState(() {
          _errorMessage = result['error'] ?? 'Verification failed';
          // Clear the code fields on error to allow retry
          for (var controller in _codeControllers) {
            controller.clear();
          }
        });
        // Focus back to first field
        _focusNodes[0].requestFocus();
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'An unexpected error occurred. Please try again.';
        // Clear the code fields on error to allow retry
        for (var controller in _codeControllers) {
          controller.clear();
        }
      });
      // Focus back to first field
      _focusNodes[0].requestFocus();
    } finally {
      if (mounted) {
        setState(() {
          _isVerifying = false;
        });
      }
    }
  }

  Future<void> _resendCode() async {
    if (_isResending || _resendCooldown > 0) return;

    setState(() {
      _isResending = true;
      _errorMessage = null;
    });

    try {
      final authService = AwsAuthService.instance;
      final result = await authService.resendVerificationEmail(widget.email);

      if (result['success'] == true) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(result['message'] ?? 'Verification code sent!'),
              backgroundColor: AppColors.gfGreen,
            ),
          );

          // Start cooldown
          _startResendCooldown();
        }
      } else {
        setState(() {
          _errorMessage = result['error'] ?? 'Failed to resend code';
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Failed to resend verification code. Please try again.';
      });
    } finally {
      if (mounted) {
        setState(() {
          _isResending = false;
        });
      }
    }
  }

  void _startResendCooldown() {
    setState(() {
      _resendCooldown = 60; // 60 seconds cooldown
    });

    _cooldownTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        _resendCooldown--;
      });

      if (_resendCooldown <= 0) {
        timer.cancel();
      }
    });
  }

  void _clearCode() {
    for (var controller in _codeControllers) {
      controller.clear();
    }
    _focusNodes[0].requestFocus();
    setState(() {
      _errorMessage = null;
    });
  }

  void _handleBackNavigation() {
    // Clear the pending verification email and reset to unauthenticated
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    authProvider.clearPendingVerification();

    // The SplashManager will automatically navigate to LoginScreen
    // when the status changes to unauthenticated
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.gfDarkBlue,
      appBar: AppBar(
        backgroundColor: AppColors.gfDarkBlue,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => _handleBackNavigation(),
        ),
        title: const Text(
          'Verify Email',
          style: TextStyle(color: Colors.white),
        ),
      ),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              const SizedBox(height: 40),

              // GameFlex Logo
              Column(
                children: [
                  SizedBox(
                    width: 80,
                    height: 80,
                    child: SvgPicture.asset(
                      'assets/images/icons/icon_teal.svg',
                      fit: BoxFit.contain,
                    ),
                  ),
                  const SizedBox(height: 16),
                  SizedBox(
                    width: 200,
                    child: SvgPicture.asset(
                      'assets/images/logos/gameflex_text.svg',
                      fit: BoxFit.fitWidth,
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 40),

              // Title
              const Text(
                'Check your email',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
                textAlign: TextAlign.center,
              ),

              const SizedBox(height: 16),

              // Description
              Text(
                'We sent a verification code to\n${widget.email}',
                style: const TextStyle(fontSize: 16, color: Colors.white70),
                textAlign: TextAlign.center,
              ),

              const SizedBox(height: 40),

              // Verification Code Input
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: List.generate(6, (index) {
                  return SizedBox(
                    width: 45,
                    height: 55,
                    child: TextField(
                      controller: _codeControllers[index],
                      focusNode: _focusNodes[index],
                      textAlign: TextAlign.center,
                      keyboardType: TextInputType.number,
                      maxLength: 1,
                      style: const TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                      decoration: InputDecoration(
                        counterText: '',
                        filled: true,
                        fillColor: AppColors.gfBlue.withValues(alpha: 0.3),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                          borderSide: BorderSide(
                            color:
                                _errorMessage != null
                                    ? Colors.red
                                    : AppColors.gfGreen.withValues(alpha: 0.5),
                          ),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                          borderSide: BorderSide(
                            color:
                                _errorMessage != null
                                    ? Colors.red
                                    : AppColors.gfGreen,
                            width: 2,
                          ),
                        ),
                      ),
                      inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                      onChanged: (value) => _onCodeChanged(index, value),
                      onTap: () {
                        // Handle paste
                        if (_codeControllers[index].text.isEmpty) {
                          Clipboard.getData(Clipboard.kTextPlain).then((data) {
                            if (data?.text != null && data!.text!.length == 6) {
                              _onCodePasted(data.text!);
                            }
                          });
                        }
                      },
                    ),
                  );
                }),
              ),

              const SizedBox(height: 20),

              // Error Message
              if (_errorMessage != null) ...[
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.red.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: Colors.red.withValues(alpha: 0.3),
                    ),
                  ),
                  child: Row(
                    children: [
                      const Icon(
                        Icons.error_outline,
                        color: Colors.red,
                        size: 20,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          _errorMessage!,
                          style: const TextStyle(color: Colors.red),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 20),
              ],

              // Verify Button
              SizedBox(
                width: double.infinity,
                height: 50,
                child: ElevatedButton(
                  onPressed:
                      _isCodeComplete && !_isVerifying ? _verifyCode : null,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.gfGreen,
                    disabledBackgroundColor: AppColors.gfGreen.withValues(
                      alpha: 0.5,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child:
                      _isVerifying
                          ? const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(
                                Colors.white,
                              ),
                            ),
                          )
                          : const Text(
                            'Verify Email',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          ),
                ),
              ),

              const SizedBox(height: 20),

              // Clear Code Button
              TextButton(
                onPressed: _clearCode,
                child: const Text(
                  'Clear Code',
                  style: TextStyle(color: Colors.white70, fontSize: 14),
                ),
              ),

              const Spacer(),

              // Resend Code
              Column(
                children: [
                  const Text(
                    "Didn't receive the code?",
                    style: TextStyle(color: Colors.white70, fontSize: 14),
                  ),
                  const SizedBox(height: 8),
                  TextButton(
                    onPressed:
                        _resendCooldown > 0 || _isResending
                            ? null
                            : _resendCode,
                    child:
                        _isResending
                            ? const SizedBox(
                              width: 16,
                              height: 16,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation<Color>(
                                  AppColors.gfGreen,
                                ),
                              ),
                            )
                            : Text(
                              _resendCooldown > 0
                                  ? 'Resend in ${_resendCooldown}s'
                                  : 'Resend Code',
                              style: TextStyle(
                                color:
                                    _resendCooldown > 0
                                        ? Colors.white38
                                        : AppColors.gfGreen,
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                  ),
                ],
              ),

              const SizedBox(height: 20),
            ],
          ),
        ),
      ),
    );
  }
}
