import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:share_plus/share_plus.dart';
import 'dart:developer' as developer;
import '../models/post_model.dart';
import '../models/comment_model.dart';
import '../components/index.dart';
import '../providers/posts_provider.dart';
import '../services/aws_comments_service.dart';
import '../widgets/comment_card.dart';
import '../widgets/add_comment_widget.dart';
import '../theme/app_theme.dart';

class PostDetailScreen extends StatefulWidget {
  final PostModel post;

  const PostDetailScreen({super.key, required this.post});

  @override
  State<PostDetailScreen> createState() => _PostDetailScreenState();
}

class _PostDetailScreenState extends State<PostDetailScreen> {
  List<CommentModel> _comments = [];
  bool _isLoading = true;
  bool _isAddingComment = false;
  String? _error;

  @override
  void initState() {
    super.initState();
    _loadComments();
  }

  @override
  void dispose() {
    // Refresh the post when leaving the detail screen to ensure
    // the feed has the latest like counts and comment counts
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final postsProvider = Provider.of<PostsProvider>(context, listen: false);
      postsProvider.refreshPost(widget.post.id);
    });
    super.dispose();
  }

  Future<void> _loadComments() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      final comments = await AwsCommentsService.instance.getComments(
        widget.post.id,
      );

      if (mounted) {
        setState(() {
          _comments = comments;
          _isLoading = false;
        });
      }
    } catch (e) {
      developer.log('PostDetailScreen: Error loading comments: $e');
      if (mounted) {
        setState(() {
          _error = 'Failed to load comments';
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _addComment(String content) async {
    try {
      setState(() {
        _isAddingComment = true;
      });

      final newComment = await AwsCommentsService.instance.createComment(
        postId: widget.post.id,
        content: content,
      );

      if (newComment != null && mounted) {
        setState(() {
          _comments.add(newComment);
          _isAddingComment = false;
        });

        // Show success message
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Comment added successfully!'),
            backgroundColor: AppColors.gfGreen,
          ),
        );
      }
    } catch (e) {
      developer.log('PostDetailScreen: Error adding comment: $e');
      if (mounted) {
        setState(() {
          _isAddingComment = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to add comment: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _deleteComment(CommentModel comment) async {
    try {
      final success = await AwsCommentsService.instance.deleteComment(
        comment.id,
      );

      if (success && mounted) {
        setState(() {
          _comments.removeWhere((c) => c.id == comment.id);
        });

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Comment deleted successfully!'),
            backgroundColor: AppColors.gfGreen,
          ),
        );
      }
    } catch (e) {
      developer.log('PostDetailScreen: Error deleting comment: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to delete comment: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _editComment(CommentModel comment) {
    final TextEditingController controller = TextEditingController(
      text: comment.content,
    );

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            backgroundColor: AppColors.gfDarkBackground,
            title: const Text(
              'Edit Comment',
              style: TextStyle(color: Colors.white),
            ),
            content: TextField(
              controller: controller,
              style: const TextStyle(color: Colors.white),
              maxLines: 3,
              decoration: const InputDecoration(
                hintText: 'Edit your comment...',
                hintStyle: TextStyle(color: Colors.grey),
                enabledBorder: OutlineInputBorder(
                  borderSide: BorderSide(color: AppColors.gfGreen),
                ),
                focusedBorder: OutlineInputBorder(
                  borderSide: BorderSide(color: AppColors.gfGreen, width: 2),
                ),
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text(
                  'Cancel',
                  style: TextStyle(color: Colors.grey),
                ),
              ),
              TextButton(
                onPressed: () async {
                  final newContent = controller.text.trim();
                  if (newContent.isNotEmpty) {
                    Navigator.pop(context);
                    await _updateComment(comment, newContent);
                  }
                },
                child: const Text(
                  'Save',
                  style: TextStyle(color: AppColors.gfGreen),
                ),
              ),
            ],
          ),
    );
  }

  Future<void> _updateComment(CommentModel comment, String newContent) async {
    try {
      final updatedComment = await AwsCommentsService.instance.updateComment(
        commentId: comment.id,
        content: newContent,
      );

      if (updatedComment != null && mounted) {
        setState(() {
          final index = _comments.indexWhere((c) => c.id == comment.id);
          if (index != -1) {
            _comments[index] = updatedComment;
          }
        });

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Comment updated successfully!'),
            backgroundColor: AppColors.gfGreen,
          ),
        );
      }
    } catch (e) {
      developer.log('PostDetailScreen: Error updating comment: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to update comment: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<PostsProvider>(
      builder: (context, postsProvider, child) {
        // Get the current post from the provider to ensure we have the latest data
        final currentPost =
            postsProvider.posts
                .where((p) => p.id == widget.post.id)
                .firstOrNull ??
            widget.post;

        return Scaffold(
          resizeToAvoidBottomInset: true,
          appBar: AppBar(
            title: const Text(
              'Post',
              style: TextStyle(
                color: AppColors.gfOffWhite,
                fontWeight: FontWeight.bold,
              ),
            ),
            leading: IconButton(
              icon: const Icon(Icons.arrow_back, color: AppColors.gfOffWhite),
              onPressed: () => Navigator.of(context).pop(),
            ),
            actions: [
              IconButton(
                icon: const Icon(Icons.share, color: AppColors.gfOffWhite),
                onPressed: () => _handleShare(context),
              ),
            ],
          ),
          body: GestureDetector(
            onTap: () {
              // Dismiss keyboard when tapping outside
              FocusScope.of(context).unfocus();
            },
            child: Column(
              children: [
                // Scrollable content area
                Expanded(
                  child: SingleChildScrollView(
                    child: Column(
                      children: [
                        // Post content - using currentPost to get latest data
                        GFPostCard(
                          post: currentPost,
                          onTap:
                              () {}, // Disable tap since we're already on the detail screen
                          showFullContent:
                              true, // Show full content without truncation
                        ),

                        // Divider
                        Container(height: 8, color: AppColors.gfDarkBackground),

                        // Comments section
                        _buildCommentsSection(),
                      ],
                    ),
                  ),
                ),

                // Add comment widget - always visible at bottom, outside scroll area
                AddCommentWidget(
                  postId: currentPost.id,
                  onCommentAdded: _addComment,
                  isLoading: _isAddingComment,
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildCommentsSection() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(
          valueColor: AlwaysStoppedAnimation<Color>(AppColors.gfGreen),
        ),
      );
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 48, color: AppColors.gfGrayText),
            const SizedBox(height: 16),
            Text(
              _error!,
              style: const TextStyle(color: AppColors.gfGrayText, fontSize: 16),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadComments,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.gfGreen,
              ),
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    if (_comments.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.chat_bubble_outline,
              size: 48,
              color: AppColors.gfGrayText,
            ),
            SizedBox(height: 16),
            Text(
              'No comments yet',
              style: TextStyle(color: AppColors.gfGrayText, fontSize: 16),
            ),
            SizedBox(height: 8),
            Text(
              'Be the first to comment!',
              style: TextStyle(color: AppColors.gfGrayText, fontSize: 14),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadComments,
      color: AppColors.gfGreen,
      backgroundColor: AppColors.gfDarkBackground,
      child: ListView.builder(
        padding: const EdgeInsets.symmetric(vertical: 8),
        itemCount: _comments.length,
        shrinkWrap: true, // Allow ListView to size itself based on content
        physics:
            const NeverScrollableScrollPhysics(), // Disable scrolling since parent handles it
        keyboardDismissBehavior: ScrollViewKeyboardDismissBehavior.onDrag,
        itemBuilder: (context, index) {
          final comment = _comments[index];
          return CommentCard(
            comment: comment,
            onEdit: () => _editComment(comment),
            onDelete: () => _deleteComment(comment),
          );
        },
      ),
    );
  }

  void _handleShare(BuildContext context) async {
    try {
      // Build the share content
      final post = widget.post;
      final author = post.authorDisplayName;
      final content = post.content;

      // Create share text
      String shareText = 'Check out this post by $author on GameFlex!\n\n';
      if (content.isNotEmpty) {
        shareText += '"$content"\n\n';
      }

      // Add channel info if available
      if (post.channelName != null && post.channelName!.isNotEmpty) {
        shareText += 'Posted in #${post.channelName}\n\n';
      }

      // Add app promotion
      shareText += 'Join the gaming community on GameFlex!';

      // Share the post content
      await Share.share(shareText, subject: 'GameFlex Post by $author');

      developer.log('Post shared successfully: ${post.id}');
    } catch (e) {
      developer.log('Error sharing post: $e');

      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to share post'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
