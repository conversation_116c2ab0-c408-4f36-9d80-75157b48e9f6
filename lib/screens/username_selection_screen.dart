import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'dart:async';
import '../providers/auth_provider.dart';
import '../theme/app_theme.dart';
import '../components/index.dart';
import '../utils/username_validator.dart';

class UsernameSelectionScreen extends StatefulWidget {
  const UsernameSelectionScreen({super.key});

  @override
  State<UsernameSelectionScreen> createState() =>
      _UsernameSelectionScreenState();
}

class _UsernameSelectionScreenState extends State<UsernameSelectionScreen> {
  final _usernameController = TextEditingController();
  final _focusNode = FocusNode();

  bool _isLoading = false;
  bool _isCheckingAvailability = false;
  UsernameValidationResult? _validationResult;
  Timer? _debounceTimer;

  @override
  void initState() {
    super.initState();
    _usernameController.addListener(_onUsernameChanged);
  }

  @override
  void dispose() {
    _usernameController.removeListener(_onUsernameChanged);
    _usernameController.dispose();
    _focusNode.dispose();
    _debounceTimer?.cancel();
    super.dispose();
  }

  void _onUsernameChanged() {
    // Cancel previous timer
    _debounceTimer?.cancel();

    // Reset validation result for immediate feedback
    final localValidation = UsernameValidator.validateLocally(
      _usernameController.text,
    );
    setState(() {
      _validationResult = localValidation;
      _isCheckingAvailability = false;
    });

    // If local validation passes, check availability after a delay
    if (localValidation.isValid && _usernameController.text.trim().isNotEmpty) {
      _debounceTimer = Timer(const Duration(milliseconds: 800), () {
        _checkUsernameAvailability();
      });
    }
  }

  Future<void> _checkUsernameAvailability() async {
    if (!mounted) return;

    setState(() {
      _isCheckingAvailability = true;
    });

    try {
      final result = await UsernameValidator.checkAvailability(
        _usernameController.text,
      );
      if (mounted) {
        setState(() {
          _validationResult = result;
          _isCheckingAvailability = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _validationResult = const UsernameValidationResult(
            isValid: false,
            isAvailable: false,
            error: 'Connection error',
            details: 'Unable to check username availability',
          );
          _isCheckingAvailability = false;
        });
      }
    }
  }

  Future<void> _setUsername() async {
    // Check if we have a valid and available username
    if (_validationResult == null || !_validationResult!.isValidAndAvailable) {
      // Force a validation check
      await _checkUsernameAvailability();
      if (_validationResult == null ||
          !_validationResult!.isValidAndAvailable) {
        return;
      }
    }

    setState(() {
      _isLoading = true;
    });

    if (!mounted) return;
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final success = await authProvider.setUsername(
      username: _usernameController.text.trim(),
    );

    setState(() {
      _isLoading = false;
    });

    if (success) {
      // Username set successfully, user will be redirected by the splash manager
    } else {
      // Error is already handled by the provider and shown in the UI
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              authProvider.errorMessage ?? 'Failed to set username',
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Widget _buildValidationIndicator() {
    if (_usernameController.text.trim().isEmpty) {
      return const SizedBox.shrink();
    }

    if (_isCheckingAvailability) {
      return Row(
        children: [
          SizedBox(
            width: 16,
            height: 16,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(AppColors.gfGreen),
            ),
          ),
          const SizedBox(width: 8),
          Text(
            'Checking availability...',
            style: TextStyle(color: AppColors.gfGrayText, fontSize: 14),
          ),
        ],
      );
    }

    if (_validationResult != null) {
      final isSuccess = _validationResult!.isValidAndAvailable;
      final isLocallyValid =
          _validationResult!.isValid && !_validationResult!.isAvailable;

      // Don't show anything for locally valid usernames that haven't been checked yet
      if (isLocallyValid) {
        return const SizedBox.shrink();
      }

      final color = isSuccess ? AppColors.gfGreen : Colors.red;
      final icon = isSuccess ? Icons.check_circle : Icons.error;
      final message =
          isSuccess
              ? UsernameValidator.getSuccessMessage(_validationResult!)
              : UsernameValidator.getErrorMessage(_validationResult!);

      return Row(
        children: [
          Icon(icon, color: color, size: 16),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              message,
              style: TextStyle(
                color: color,
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      );
    }

    return const SizedBox.shrink();
  }

  @override
  Widget build(BuildContext context) {
    final canContinue =
        _validationResult?.isValidAndAvailable == true &&
        !_isCheckingAvailability;

    return Scaffold(
      backgroundColor: AppColors.gfDarkBackground100,
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Header Icon
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  color: AppColors.gfDarkBackground,
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Icon(
                  Icons.person_add_rounded,
                  size: 40,
                  color: AppColors.gfGreen,
                ),
              ),
              const SizedBox(height: 32),

              // Title
              Text(
                'Choose Your Username',
                style: TextStyle(
                  fontSize: 28,
                  fontWeight: FontWeight.bold,
                  color: AppColors.gfOffWhite,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),

              // Subtitle
              Text(
                'Pick a unique username that others will see.\nIt must be 4-32 characters long.',
                style: TextStyle(
                  fontSize: 16,
                  color: AppColors.gfGrayText,
                  height: 1.4,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 48),
              // Username Input
              GFTextField(
                label: 'Username',
                hint: 'Enter your username',
                controller: _usernameController,
                onChanged: (value) {
                  // The listener will handle this
                },
              ),
              const SizedBox(height: 12),

              // Validation Indicator
              _buildValidationIndicator(),
              const SizedBox(height: 32),

              // Continue Button
              GFButton(
                text: 'Continue',
                onPressed: canContinue && !_isLoading ? _setUsername : null,
                isEnabled: canContinue && !_isLoading,
                type: GFButtonType.primary,
              ),

              if (_isLoading) ...[
                const SizedBox(height: 16),
                Center(
                  child: SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(
                        AppColors.gfGreen,
                      ),
                    ),
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}
