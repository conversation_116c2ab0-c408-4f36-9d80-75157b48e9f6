import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:pro_image_editor/pro_image_editor.dart';
import 'package:image_picker/image_picker.dart';
import '../models/post_model.dart';
import '../models/reflex_model.dart';
import '../providers/reflex_provider.dart';
import '../providers/posts_provider.dart';

import '../components/upload_progress_dialog.dart';
import '../theme/app_theme.dart';
import '../utils/app_logger.dart';
import 'video_frame_selector_screen.dart';

class ReflexCreationScreen extends StatefulWidget {
  final PostModel originalPost;

  const ReflexCreationScreen({super.key, required this.originalPost});

  @override
  State<ReflexCreationScreen> createState() => _ReflexCreationScreenState();
}

class _ReflexCreationScreenState extends State<ReflexCreationScreen> {
  bool _isCreating = false;
  Uint8List? _editedImageBytes;
  String? _uploadedMediaId;

  @override
  void initState() {
    super.initState();
    // Clear any previous state to ensure fresh reflex creation
    _clearState();
    // Automatically open image source dialog when screen loads
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _showImageSourceDialog();
    });
  }

  /// Clear all state variables to ensure fresh reflex creation
  void _clearState() {
    final hadPreviousData =
        _editedImageBytes != null || _uploadedMediaId != null;
    _editedImageBytes = null;
    _uploadedMediaId = null;
    _isCreating = false;
    AppLogger.debug(
      'ReflexCreationScreen: State cleared for fresh reflex creation (had previous data: $hadPreviousData)',
    );
  }

  @override
  void dispose() {
    // Clear state on dispose to prevent any memory leaks
    _clearState();
    super.dispose();
  }

  Future<void> _showImageSourceDialog() async {
    if (!mounted) return;

    // Check if the original post is a video
    final isVideoPost = widget.originalPost.mediaType == 'video';

    final choice = await showDialog<String>(
      context: context,
      builder:
          (context) => AlertDialog(
            backgroundColor: AppColors.gfDarkBackground,
            title: Text(
              isVideoPost ? 'Choose Frame Source' : 'Choose Image Source',
              style: const TextStyle(color: AppColors.gfOffWhite),
            ),
            content: Text(
              isVideoPost
                  ? 'Would you like to select a frame from the original video or choose a new image?'
                  : 'Would you like to edit the original post image or select a new image?',
              style: const TextStyle(color: AppColors.gfGrayText),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop('original'),
                child: Text(
                  isVideoPost ? 'Select Video Frame' : 'Original Image',
                  style: const TextStyle(color: AppColors.gfGreen),
                ),
              ),
              TextButton(
                onPressed: () => Navigator.of(context).pop('new'),
                child: const Text(
                  'New Image',
                  style: TextStyle(color: AppColors.gfTeal),
                ),
              ),
              TextButton(
                onPressed: () => Navigator.of(context).pop('cancel'),
                child: const Text(
                  'Cancel',
                  style: TextStyle(color: AppColors.gfGrayText),
                ),
              ),
            ],
          ),
    );

    if (choice == 'original') {
      if (isVideoPost) {
        await _openVideoFrameSelector();
      } else {
        await _openImageEditorWithOriginal();
      }
    } else if (choice == 'new') {
      await _openImageEditorWithNew();
    } else {
      // User cancelled, go back
      if (mounted) {
        Navigator.of(context).pop();
      }
    }
  }

  Future<void> _openImageEditorWithOriginal() async {
    try {
      AppLogger.debug(
        'ReflexCreationScreen: Opening image editor with original image',
      );

      // Get the original post's media URL
      final mediaUrl = await widget.originalPost.getMediaUrl();
      if (mediaUrl == null) {
        _showError('No image available for this post');
        return;
      }

      await _openProImageEditor(mediaUrl: mediaUrl);
    } catch (e, stackTrace) {
      AppLogger.error(
        'ReflexCreationScreen: Error opening image editor with original',
        error: e,
        stackTrace: stackTrace,
      );
      _showError('Failed to open image editor: $e');
    }
  }

  Future<void> _openImageEditorWithNew() async {
    try {
      AppLogger.debug(
        'ReflexCreationScreen: Opening image editor with new image',
      );

      // Use image picker to select a new image
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 85,
      );

      if (image == null) {
        // User cancelled image selection, go back
        if (mounted) {
          Navigator.of(context).pop();
        }
        return;
      }

      // Convert to file path for pro image editor
      await _openProImageEditor(imagePath: image.path);
    } catch (e, stackTrace) {
      AppLogger.error(
        'ReflexCreationScreen: Error opening image editor with new image',
        error: e,
        stackTrace: stackTrace,
      );
      _showError('Failed to open image editor: $e');
    }
  }

  Future<void> _openProImageEditor({
    String? mediaUrl,
    String? imagePath,
  }) async {
    try {
      if (!mounted) return;

      final Widget editor;
      if (mediaUrl != null) {
        editor = ProImageEditor.network(
          mediaUrl,
          callbacks: ProImageEditorCallbacks(
            onImageEditingComplete: (bytes) async {
              Navigator.of(context).pop(bytes);
            },
          ),
          configs: ProImageEditorConfigs(
            designMode: ImageEditorDesignMode.material,
            theme: ThemeData(
              useMaterial3: true,
              colorScheme: ColorScheme.fromSeed(
                seedColor: AppColors.gfGreen,
                brightness: Brightness.dark,
              ),
              scaffoldBackgroundColor: AppColors.darkBlue,
            ),
          ),
        );
      } else if (imagePath != null) {
        editor = ProImageEditor.file(
          File(imagePath),
          callbacks: ProImageEditorCallbacks(
            onImageEditingComplete: (bytes) async {
              Navigator.of(context).pop(bytes);
            },
          ),
          configs: ProImageEditorConfigs(
            designMode: ImageEditorDesignMode.material,
            theme: ThemeData(
              useMaterial3: true,
              colorScheme: ColorScheme.fromSeed(
                seedColor: AppColors.gfGreen,
                brightness: Brightness.dark,
              ),
              scaffoldBackgroundColor: AppColors.darkBlue,
            ),
          ),
        );
      } else {
        _showError('No image source provided');
        return;
      }

      final editedBytes = await Navigator.of(
        context,
      ).push<Uint8List>(MaterialPageRoute(builder: (context) => editor));

      if (editedBytes != null) {
        setState(() {
          _editedImageBytes = editedBytes;
        });
        AppLogger.debug('ReflexCreationScreen: Image edited successfully');
        // Automatically create the reflex after editing
        await _createReflex();
      } else {
        // User cancelled editing, go back
        if (mounted) {
          Navigator.of(context).pop();
        }
      }
    } catch (e, stackTrace) {
      AppLogger.error(
        'ReflexCreationScreen: Error in pro image editor',
        error: e,
        stackTrace: stackTrace,
      );
      _showError('Failed to edit image: $e');
    }
  }

  Future<void> _openVideoFrameSelector() async {
    try {
      AppLogger.debug(
        'ReflexCreationScreen: Opening video frame selector for original video',
      );

      // Get the original post's media URL
      final mediaUrl = await widget.originalPost.getMediaUrl();
      if (mediaUrl == null) {
        _showError('Original video not found');
        return;
      }

      if (!mounted) return;

      // Open the video frame selector
      await Navigator.of(context).push(
        MaterialPageRoute(
          builder:
              (context) => VideoFrameSelectorScreen(
                videoUrl: mediaUrl,
                onFrameSelected: (frameBytes) async {
                  // Frame selected, now open image editor with the frame
                  await _openProImageEditorWithBytes(frameBytes);
                },
              ),
        ),
      );
    } catch (e, stackTrace) {
      AppLogger.error(
        'ReflexCreationScreen: Error opening video frame selector',
        error: e,
        stackTrace: stackTrace,
      );
      _showError('Failed to open video frame selector: $e');
    }
  }

  Future<void> _openProImageEditorWithBytes(Uint8List imageBytes) async {
    try {
      if (!mounted) return;

      // Create a temporary file from the frame bytes with unique name
      final tempDir = Directory.systemTemp;
      final uniqueId = DateTime.now().microsecondsSinceEpoch;
      final tempFile = File(
        '${tempDir.path}/video_frame_${uniqueId}_${widget.originalPost.id}.jpg',
      );
      await tempFile.writeAsBytes(imageBytes);

      AppLogger.debug(
        'ReflexCreationScreen: Created temp file for video frame: ${tempFile.path} (${imageBytes.length} bytes)',
      );

      final editor = ProImageEditor.file(
        tempFile,
        callbacks: ProImageEditorCallbacks(
          onImageEditingComplete: (bytes) async {
            Navigator.of(context).pop(bytes);
          },
        ),
        configs: ProImageEditorConfigs(
          designMode: ImageEditorDesignMode.material,
          theme: ThemeData(
            useMaterial3: true,
            colorScheme: ColorScheme.fromSeed(
              seedColor: AppColors.gfGreen,
              brightness: Brightness.dark,
            ),
            scaffoldBackgroundColor: AppColors.darkBlue,
          ),
        ),
      );

      if (!mounted) return;

      final editedBytes = await Navigator.of(
        context,
      ).push<Uint8List>(MaterialPageRoute(builder: (context) => editor));

      // Clean up temp file
      if (await tempFile.exists()) {
        await tempFile.delete();
      }

      if (editedBytes != null) {
        setState(() {
          _editedImageBytes = editedBytes;
        });
        AppLogger.debug(
          'ReflexCreationScreen: Video frame edited successfully',
        );
        // Automatically create the reflex after editing
        await _createReflex();
      } else {
        // User cancelled editing, go back
        if (mounted) {
          Navigator.of(context).pop();
        }
      }
    } catch (e, stackTrace) {
      AppLogger.error(
        'ReflexCreationScreen: Error opening image editor with frame bytes',
        error: e,
        stackTrace: stackTrace,
      );
      _showError('Failed to open image editor: $e');
    }
  }

  Future<void> _uploadEditedImage() async {
    if (_editedImageBytes == null) return;

    try {
      AppLogger.debug(
        'ReflexCreationScreen: Uploading edited image with ${_editedImageBytes!.length} bytes',
      );

      // Create a temporary file from the edited bytes with unique name
      final tempDir = Directory.systemTemp;
      final uniqueId = DateTime.now().microsecondsSinceEpoch;
      final tempFile = File(
        '${tempDir.path}/reflex_${uniqueId}_${widget.originalPost.id}.jpg',
      );
      await tempFile.writeAsBytes(_editedImageBytes!);

      AppLogger.debug(
        'ReflexCreationScreen: Created temp file for upload: ${tempFile.path}',
      );

      // Upload the image as reflex media type using enhanced service
      if (!mounted) return;
      final uploadResult = await showEnhancedUploadDialog(
        context: context,
        file: tempFile,
        fileName: 'reflex_${uniqueId}_${widget.originalPost.id}.jpg',
        fileType: 'image/jpeg',
        mediaType: 'reflex',
        title: 'Uploading Reflex Image',
      );

      if (uploadResult != null && uploadResult.isSuccess) {
        setState(() {
          _uploadedMediaId = uploadResult.mediaId;
        });
        AppLogger.debug(
          'ReflexCreationScreen: Image uploaded with ID: ${uploadResult.mediaId}',
        );
      } else {
        // Handle upload failure with detailed error
        if (uploadResult?.isRejected == true) {
          _showDetailedError(
            'Content Rejected',
            uploadResult!.userFriendlyMessage,
            isRejection: true,
          );
        } else if (uploadResult?.isLimitExceeded == true) {
          _showDetailedError(
            'Upload Limit Exceeded',
            uploadResult!.userFriendlyMessage,
            isLimitExceeded: true,
          );
        } else {
          _showError(
            uploadResult?.userFriendlyMessage ?? 'Failed to upload image',
          );
        }
      }

      // Clean up temp file
      if (await tempFile.exists()) {
        await tempFile.delete();
      }
    } catch (e, stackTrace) {
      AppLogger.error(
        'ReflexCreationScreen: Error uploading image',
        error: e,
        stackTrace: stackTrace,
      );
      _showError('Failed to upload image: $e');
    }
  }

  Future<void> _createReflex() async {
    if (_isCreating) return;

    // Validate input - we must have edited image bytes
    if (_editedImageBytes == null) {
      _showError('Please edit an image first');
      return;
    }

    setState(() {
      _isCreating = true;
    });

    try {
      AppLogger.debug(
        'ReflexCreationScreen: Creating reflex for post ${widget.originalPost.id} with image bytes length: ${_editedImageBytes?.length ?? 0}',
      );

      // Upload image if we have edited bytes but no media ID yet
      if (_uploadedMediaId == null) {
        await _uploadEditedImage();
        if (_uploadedMediaId == null) {
          setState(() {
            _isCreating = false;
          });
          return;
        }
      }

      final request = CreateReflexRequest(
        postId: widget.originalPost.id,
        mediaId: _uploadedMediaId,
        flareData: null, // No flare data for image reflexes
        textOverlay: null, // No text overlay for now
        reflexType:
            ReflexType.customImage, // Always custom image since we edited it
      );

      if (!mounted) return;
      final reflexProvider = Provider.of<ReflexProvider>(
        context,
        listen: false,
      );
      final reflex = await reflexProvider.createReflex(request);

      if (reflex != null) {
        // Update the post's reflex count
        if (!mounted) return;
        final postsProvider = Provider.of<PostsProvider>(
          context,
          listen: false,
        );
        await postsProvider.refreshPost(widget.originalPost.id);

        AppLogger.debug('ReflexCreationScreen: Reflex created successfully');

        // Clear state before navigation to ensure no data persists
        _clearState();

        if (mounted) {
          Navigator.of(context).pop(reflex);
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Reflex created successfully!'),
              backgroundColor: AppColors.gfGreen,
            ),
          );
        }
      } else {
        _showError('Failed to create reflex');
      }
    } catch (e, stackTrace) {
      AppLogger.error(
        'ReflexCreationScreen: Error creating reflex',
        error: e,
        stackTrace: stackTrace,
      );
      _showError('Failed to create reflex: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isCreating = false;
        });
      }
    }
  }

  void _showError(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(message), backgroundColor: Colors.red),
      );
    }
  }

  void _showDetailedError(
    String title,
    String message, {
    bool isRejection = false,
    bool isLimitExceeded = false,
  }) {
    if (!mounted) return;

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            backgroundColor: AppColors.gfDarkBackground,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
            title: Row(
              children: [
                Icon(
                  isRejection
                      ? Icons.block
                      : (isLimitExceeded ? Icons.warning : Icons.error),
                  color:
                      isRejection
                          ? Colors.red
                          : (isLimitExceeded ? Colors.orange : Colors.red),
                  size: 24,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    title,
                    style: TextStyle(
                      color:
                          isRejection
                              ? Colors.red
                              : (isLimitExceeded ? Colors.orange : Colors.red),
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            content: Container(
              constraints: const BoxConstraints(maxWidth: 300),
              child: Text(
                message,
                style: const TextStyle(
                  color: AppColors.gfGrayText,
                  height: 1.4,
                ),
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text(
                  'OK',
                  style: TextStyle(
                    color: AppColors.gfTeal,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.gfDarkBackground,
      appBar: AppBar(
        title: const Text(
          'Create Reflex',
          style: TextStyle(
            color: AppColors.gfGreen,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: AppColors.gfDarkBackground,
        iconTheme: const IconThemeData(color: AppColors.gfOffWhite),
      ),
      body: Center(
        child:
            _isCreating
                ? const Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CircularProgressIndicator(
                      valueColor: AlwaysStoppedAnimation<Color>(
                        AppColors.gfGreen,
                      ),
                    ),
                    SizedBox(height: 16),
                    Text(
                      'Creating your reflex...',
                      style: TextStyle(
                        color: AppColors.gfOffWhite,
                        fontSize: 16,
                      ),
                    ),
                  ],
                )
                : const Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.edit, color: AppColors.gfGreen, size: 64),
                    SizedBox(height: 16),
                    Text(
                      'Choose your image source...',
                      style: TextStyle(
                        color: AppColors.gfOffWhite,
                        fontSize: 16,
                      ),
                    ),
                  ],
                ),
      ),
    );
  }
}
