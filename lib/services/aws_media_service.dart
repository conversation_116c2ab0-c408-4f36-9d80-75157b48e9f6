import 'dart:developer' as developer;
import 'dart:io';
import 'package:http/http.dart' as http;
import '../utils/app_logger.dart';
import 'api_service.dart';

/// AWS media service for handling R2 uploads via API Gateway
class AwsMediaService {
  static AwsMediaService? _instance;
  static AwsMediaService get instance => _instance ??= AwsMediaService._();

  AwsMediaService._();

  /// Upload media file to S3 using presigned URL
  Future<String?> uploadMedia({
    required File file,
    required String fileName,
    required String fileType,
    String mediaType = 'image',
  }) async {
    try {
      developer.log('AwsMediaService: Starting media upload for $fileName');

      // Step 1: Request upload URL from backend
      final uploadUrlResponse = await _requestUploadUrl(
        fileName: fileName,
        fileType: fileType,
        fileSize: await file.length(),
        mediaType: mediaType,
      );

      if (uploadUrlResponse == null) {
        throw Exception('Failed to get upload URL');
      }

      final mediaId = uploadUrlResponse['mediaId'] as String;
      final uploadUrl = uploadUrlResponse['uploadUrl'] as String;

      developer.log('AwsMediaService: Got upload URL for media ID: $mediaId');

      // Step 2: Upload file directly to S3
      final uploadSuccess = await _uploadToS3(
        file: file,
        uploadUrl: uploadUrl,
        fileType: fileType,
      );

      if (!uploadSuccess) {
        throw Exception('Failed to upload file to S3');
      }

      developer.log('AwsMediaService: File uploaded successfully to S3');

      // Step 3: Update media status to 'uploaded'
      developer.log('AwsMediaService: Updating media status to uploaded...');
      final statusUpdateSuccess = await _updateMediaStatus(
        mediaId: mediaId,
        status: 'uploaded',
      );

      if (!statusUpdateSuccess) {
        developer.log(
          'AwsMediaService: Warning - failed to update media status',
        );
        // Don't throw error here as the file was uploaded successfully
      } else {
        developer.log('AwsMediaService: Media status updated successfully');
      }

      developer.log('AwsMediaService: Media upload completed successfully');
      return mediaId;
    } catch (e) {
      developer.log('AwsMediaService: Error uploading media: $e');
      AppLogger.error('Error uploading media', error: e);
      return null;
    }
  }

  /// Request upload URL from backend
  Future<Map<String, dynamic>?> _requestUploadUrl({
    required String fileName,
    required String fileType,
    required int fileSize,
    required String mediaType,
  }) async {
    try {
      final response = await ApiService.instance.makeAuthenticatedRequest(
        method: 'POST',
        path: '/media/upload',
        body: {
          'fileName': fileName,
          'fileType': fileType,
          'fileSize': fileSize,
          'mediaType': mediaType,
        },
      );

      final data = ApiService.instance.parseResponse(response);
      return data;
    } catch (e) {
      developer.log('AwsMediaService: Error requesting upload URL: $e');
      return null;
    }
  }

  /// Upload file directly to S3 using presigned URL
  Future<bool> _uploadToS3({
    required File file,
    required String uploadUrl,
    required String fileType,
  }) async {
    try {
      final fileBytes = await file.readAsBytes();
      developer.log('AwsMediaService: File size: ${fileBytes.length} bytes');
      developer.log('AwsMediaService: Content-Type: $fileType');
      developer.log(
        'AwsMediaService: Upload URL: ${uploadUrl.substring(0, 100)}...',
      );

      final response = await http.put(
        Uri.parse(uploadUrl),
        headers: {
          'Content-Type': fileType,
          'Content-Length': fileBytes.length.toString(),
        },
        body: fileBytes,
      );

      developer.log(
        'AwsMediaService: S3 response status: ${response.statusCode}',
      );
      developer.log(
        'AwsMediaService: S3 response headers: ${response.headers}',
      );

      if (response.statusCode == 200 || response.statusCode == 204) {
        developer.log('AwsMediaService: S3 upload successful');
        return true;
      } else {
        developer.log(
          'AwsMediaService: S3 upload failed with status: ${response.statusCode}',
        );
        developer.log('AwsMediaService: S3 upload response: ${response.body}');
        return false;
      }
    } catch (e) {
      developer.log('AwsMediaService: Error uploading to S3: $e');
      return false;
    }
  }

  /// Update media status in backend
  Future<bool> _updateMediaStatus({
    required String mediaId,
    required String status,
  }) async {
    try {
      developer.log(
        'AwsMediaService: Calling PUT /media/$mediaId with status: $status',
      );
      final response = await ApiService.instance.makeAuthenticatedRequest(
        method: 'PUT',
        path: '/media/$mediaId',
        body: {'status': status},
      );

      final data = ApiService.instance.parseResponse(response);
      developer.log(
        'AwsMediaService: Media status updated to $status, response: $data',
      );
      return true;
    } catch (e) {
      developer.log('AwsMediaService: Error updating media status: $e');
      return false;
    }
  }

  /// Get media information
  Future<Map<String, dynamic>?> getMedia(String mediaId) async {
    try {
      developer.log('AwsMediaService: Getting media info for $mediaId');

      final response = await ApiService.instance.makeAuthenticatedRequest(
        method: 'GET',
        path: '/media/$mediaId',
      );

      final data = ApiService.instance.parseResponse(response);
      return data['media'] as Map<String, dynamic>?;
    } catch (e) {
      developer.log('AwsMediaService: Error getting media: $e');
      AppLogger.error('Error getting media', error: e);
      return null;
    }
  }

  /// Delete media
  Future<bool> deleteMedia(String mediaId) async {
    try {
      developer.log('AwsMediaService: Deleting media $mediaId');

      final response = await ApiService.instance.makeAuthenticatedRequest(
        method: 'DELETE',
        path: '/media/$mediaId',
      );

      ApiService.instance.parseResponse(response);
      developer.log('AwsMediaService: Media deleted successfully');
      return true;
    } catch (e) {
      developer.log('AwsMediaService: Error deleting media: $e');
      AppLogger.error('Error deleting media', error: e);
      return false;
    }
  }
}
