import 'dart:async';
import 'dart:developer' as developer;
import '../models/post_model.dart';
import '../utils/app_logger.dart';
import 'api_service.dart';
import 'newrelic_service.dart';

/// Service for tracking post engagement metrics for the TikTok-style algorithm
class EngagementTrackingService {
  static EngagementTrackingService? _instance;
  static EngagementTrackingService get instance => _instance ??= EngagementTrackingService._();

  EngagementTrackingService._();

  // Track active post views
  final Map<String, _PostViewSession> _activeSessions = {};

  /// Start tracking a post view
  void startViewTracking(PostModel post) {
    final postId = post.id;
    
    // End any existing session for this post
    if (_activeSessions.containsKey(postId)) {
      _endViewSession(postId, interacted: false);
    }

    // Start new session
    _activeSessions[postId] = _PostViewSession(
      postId: postId,
      startTime: DateTime.now(),
    );

    AppLogger.debug('EngagementTracking: Started tracking view for post $postId');

    // Record New Relic event for analytics
    NewRelicService.instance.recordCustomEvent('post_view_started', {
      'post_id': postId,
      'user_id': post.userId,
      'has_media': post.hasMedia,
      'media_type': post.mediaType ?? 'none',
    });
  }

  /// Update scroll position for current post
  void updateScrollPosition(String postId, double scrollPosition) {
    final session = _activeSessions[postId];
    if (session != null) {
      session.maxScrollPosition = scrollPosition > session.maxScrollPosition 
          ? scrollPosition 
          : session.maxScrollPosition;
    }
  }

  /// Mark that user interacted with the post
  void markInteraction(String postId, String interactionType) {
    final session = _activeSessions[postId];
    if (session != null) {
      session.interacted = true;
      session.interactions.add(interactionType);
      
      AppLogger.debug('EngagementTracking: Marked interaction $interactionType for post $postId');
      
      // Record New Relic event
      NewRelicService.instance.recordCustomEvent('post_interaction', {
        'post_id': postId,
        'interaction_type': interactionType,
        'view_duration_ms': DateTime.now().difference(session.startTime).inMilliseconds,
      });
    }
  }

  /// End tracking for a specific post
  void endViewTracking(String postId, {bool interacted = false}) {
    _endViewSession(postId, interacted: interacted);
  }

  /// End tracking for all posts (e.g., when app goes to background)
  void endAllViewTracking() {
    final postIds = List<String>.from(_activeSessions.keys);
    for (final postId in postIds) {
      _endViewSession(postId, interacted: false);
    }
  }

  /// Internal method to end a view session and send to backend
  void _endViewSession(String postId, {required bool interacted}) {
    final session = _activeSessions.remove(postId);
    if (session == null) return;

    final viewDuration = DateTime.now().difference(session.startTime).inMilliseconds;
    final scrollPosition = session.maxScrollPosition;
    final hasInteracted = session.interacted || interacted;

    AppLogger.debug(
      'EngagementTracking: Ending view for post $postId - '
      'Duration: ${viewDuration}ms, Scroll: $scrollPosition, Interacted: $hasInteracted'
    );

    // Send to backend asynchronously
    _sendViewTrackingData(postId, viewDuration, scrollPosition, hasInteracted);

    // Record New Relic event
    NewRelicService.instance.recordCustomEvent('post_view_ended', {
      'post_id': postId,
      'view_duration_ms': viewDuration,
      'scroll_position': scrollPosition,
      'interacted': hasInteracted,
      'interaction_count': session.interactions.length,
      'interaction_types': session.interactions.join(','),
    });
  }

  /// Send view tracking data to backend
  Future<void> _sendViewTrackingData(
    String postId,
    int viewDuration,
    double scrollPosition,
    bool interacted,
  ) async {
    try {
      // Only send if view duration is meaningful (> 100ms)
      if (viewDuration < 100) {
        AppLogger.debug('EngagementTracking: Skipping very short view ($viewDuration ms)');
        return;
      }

      developer.log('EngagementTracking: Sending view data for post $postId');

      final response = await ApiService.instance.makeAuthenticatedRequest(
        method: 'POST',
        path: '/posts/$postId/view',
        body: {
          'viewDuration': viewDuration,
          'scrollPosition': scrollPosition,
          'interacted': interacted,
        },
      );

      ApiService.instance.parseResponse(response);
      
      AppLogger.debug('EngagementTracking: Successfully sent view data for post $postId');

    } catch (e) {
      AppLogger.error('EngagementTracking: Failed to send view data for post $postId', error: e);
      developer.log('EngagementTracking: Error sending view data: $e');
      
      // Record error in New Relic
      NewRelicService.instance.recordError(e, StackTrace.current, attributes: {
        'post_id': postId,
        'view_duration': viewDuration,
        'scroll_position': scrollPosition,
        'interacted': interacted,
      });
    }
  }

  /// Get engagement-based feed (TikTok-style algorithm)
  Future<List<PostModel>> getEngagementBasedFeed({
    int limit = 10,
  }) async {
    try {
      AppLogger.debug('EngagementTracking: Getting engagement-based feed (limit: $limit)');

      final response = await ApiService.instance.makeAuthenticatedRequest(
        method: 'GET',
        path: '/posts/engagement-feed?limit=$limit',
      );

      final data = ApiService.instance.parseResponse(response);
      final posts = (data['posts'] as List<dynamic>?)
          ?.map((json) => PostModel.fromJson(json as Map<String, dynamic>))
          .toList() ?? [];

      AppLogger.debug('EngagementTracking: Retrieved ${posts.length} posts from engagement feed');

      // Record analytics
      NewRelicService.instance.recordCustomEvent('engagement_feed_loaded', {
        'posts_count': posts.length,
        'algorithm': data['algorithm'] ?? 'unknown',
        'has_more': data['hasMore'] ?? false,
      });

      return posts;

    } catch (e) {
      AppLogger.error('EngagementTracking: Failed to get engagement-based feed', error: e);
      developer.log('EngagementTracking: Error getting engagement feed: $e');
      rethrow;
    }
  }

  /// Clean up resources
  void dispose() {
    endAllViewTracking();
  }
}

/// Internal class to track a post view session
class _PostViewSession {
  final String postId;
  final DateTime startTime;
  double maxScrollPosition = 0.0;
  bool interacted = false;
  final List<String> interactions = [];

  _PostViewSession({
    required this.postId,
    required this.startTime,
  });
}
