import 'dart:async';
import 'dart:convert';
import 'dart:developer' as developer;
import 'dart:math' as math;

import 'package:shared_preferences/shared_preferences.dart';
import 'api_service.dart';
import '../models/user_model.dart';
import '../utils/app_logger.dart';

/// AWS Cognito authentication service
class AwsAuthService {
  static AwsAuthService? _instance;
  static AwsAuthService get instance => _instance ??= AwsAuthService._();

  AwsAuthService._();

  // Storage keys for JWT tokens
  static const String _accessTokenKey = 'aws_access_token';
  static const String _refreshTokenKey = 'aws_refresh_token';
  static const String _idTokenKey = 'aws_id_token';
  static const String _userDataKey = 'aws_user_data';
  static const String _tokenExpirationKey = 'aws_token_expiration';

  // Auth state stream
  final StreamController<AuthState> _authStateController =
      StreamController<AuthState>.broadcast();
  Stream<AuthState> get authStateChanges => _authStateController.stream;

  String? _accessToken;
  String? _refreshToken;
  String? _idToken;
  AwsUser? _currentUser;
  DateTime? _tokenExpiration;

  /// Check if user is currently authenticated
  bool get isAuthenticated {
    if (_accessToken == null || _currentUser == null) {
      return false;
    }

    // Validate token format (JWT should have 3 parts separated by dots)
    final tokenParts = _accessToken!.split('.');
    if (tokenParts.length != 3) {
      developer.log('AwsAuthService: isAuthenticated - Invalid token format');
      return false;
    }

    return true;
  }

  /// Get current user
  AwsUser? get currentUser => _currentUser;

  /// Get current access token
  String? get accessToken => _accessToken;

  /// Check if the current token is expired or will expire soon (within 5 minutes)
  bool get isTokenExpired {
    if (_tokenExpiration == null) return false;
    final now = DateTime.now();
    final bufferTime = const Duration(minutes: 5);
    return now.isAfter(_tokenExpiration!.subtract(bufferTime));
  }

  /// Get a valid access token, refreshing if necessary
  Future<String?> getValidAccessToken() async {
    if (_accessToken == null) {
      developer.log('AwsAuthService: No access token available');
      return null;
    }

    // Validate token format (JWT should have 3 parts separated by dots)
    final tokenParts = _accessToken!.split('.');
    if (tokenParts.length != 3) {
      developer.log(
        'AwsAuthService: Invalid token format - not a JWT. Parts: ${tokenParts.length}',
      );
      developer.log('AwsAuthService: Token content: $_accessToken');
      // Clear invalid token and try to refresh
      await _clearAuthData();
      _authStateController.add(AuthState.signedOut);
      return null;
    }

    // Debug: Log token info
    developer.log(
      'AwsAuthService: Current token length: ${_accessToken!.length}',
    );
    developer.log(
      'AwsAuthService: Token starts with: ${_accessToken!.substring(0, math.min(50, _accessToken!.length))}...',
    );

    if (isTokenExpired) {
      developer.log(
        'AwsAuthService: Token is expired or expiring soon, refreshing...',
      );
      final refreshSuccess = await refreshToken();
      if (!refreshSuccess) {
        developer.log('AwsAuthService: Token refresh failed');
        return null;
      }
    }

    return _accessToken;
  }

  /// Sign up with email and password
  Future<AwsAuthResponse> signUp({
    required String email,
    required String password,
    String? firstName,
    String? lastName,
  }) async {
    try {
      developer.log('AwsAuthService: Starting sign up for email: $email');
      AppLogger.auth('Starting sign up for email: $email');

      final body = {'email': email, 'password': password};

      if (firstName != null) body['firstName'] = firstName;
      if (lastName != null) body['lastName'] = lastName;

      final response = await ApiService.instance.makeRequest(
        method: 'POST',
        path: '/auth/signup',
        body: body,
      );

      final data = ApiService.instance.parseResponse(response);

      developer.log('AwsAuthService: Sign up successful');
      AppLogger.auth('Sign up successful');

      return AwsAuthResponse(
        success: true,
        message: data['message'] as String? ?? 'User created successfully',
        user: data['user'] != null ? AwsUser.fromJson(data['user']) : null,
      );
    } catch (e) {
      developer.log('AwsAuthService: Sign up failed: $e');
      AppLogger.error('Sign up failed', error: e);

      String errorMessage;
      if (e is ApiException) {
        errorMessage = e.userFriendlyMessage;
      } else {
        errorMessage = e.toString();
      }

      return AwsAuthResponse(success: false, message: errorMessage);
    }
  }

  /// Set username for authenticated user
  Future<AwsAuthResponse> setUsername({required String username}) async {
    try {
      developer.log('AwsAuthService: Setting username: $username');
      AppLogger.auth('Setting username: $username');

      final response = await ApiService.instance.makeAuthenticatedRequest(
        method: 'POST',
        path: '/auth/set-username',
        body: {'username': username},
      );

      final data = ApiService.instance.parseResponse(response);

      developer.log('AwsAuthService: Username set successfully');
      AppLogger.auth('Username set successfully');

      // Update current user with new username
      if (_currentUser != null) {
        _currentUser = AwsUser(
          id: _currentUser!.id,
          email: _currentUser!.email,
          username: username,
          displayName: _currentUser!.displayName,
          avatarUrl: _currentUser!.avatarUrl,
          isVerified: _currentUser!.isVerified,
        );
      }

      return AwsAuthResponse(
        success: true,
        message: data['message'] ?? 'Username set successfully',
        user: _currentUser,
      );
    } catch (e, stackTrace) {
      developer.log(
        'AwsAuthService: Set username failed',
        error: e,
        stackTrace: stackTrace,
      );
      AppLogger.error(
        'AwsAuthService: SET USERNAME ERROR',
        error: e,
        stackTrace: stackTrace,
      );

      String errorMessage;
      if (e is ApiException) {
        errorMessage = e.userFriendlyMessage;
      } else {
        errorMessage = e.toString();
      }

      return AwsAuthResponse(success: false, message: errorMessage);
    }
  }

  /// Sign in with email and password
  Future<AwsAuthResponse> signIn({
    required String email,
    required String password,
  }) async {
    try {
      developer.log('AwsAuthService: Starting sign in for email: $email');
      AppLogger.auth('Starting sign in for email: $email');

      final response = await ApiService.instance.makeRequest(
        method: 'POST',
        path: '/auth/signin',
        body: {'email': email, 'password': password},
      );

      final data = ApiService.instance.parseResponse(response);

      // Extract tokens
      final tokens = data['tokens'] as Map<String, dynamic>?;
      if (tokens != null) {
        _accessToken = tokens['accessToken'] as String?;
        _refreshToken = tokens['refreshToken'] as String?;
        _idToken = tokens['idToken'] as String?;

        // Extract token expiration from access token
        if (_accessToken != null) {
          _tokenExpiration = _extractTokenExpiration(_accessToken!);
        }
      }

      // Extract user data
      final userData = data['user'] as Map<String, dynamic>?;
      if (userData != null) {
        _currentUser = AwsUser.fromJson(userData);
      }

      // Store tokens and user data
      await _storeAuthData();

      developer.log('AwsAuthService: Sign in successful');
      AppLogger.auth('Sign in successful');

      // Check if username is required
      final requiresUsername = data['requiresUsername'] as bool? ?? false;

      // Notify listeners
      _authStateController.add(AuthState.signedIn);

      return AwsAuthResponse(
        success: true,
        message: data['message'] as String? ?? 'Authentication successful',
        user: _currentUser,
        tokens: tokens,
        requiresUsername: requiresUsername,
      );
    } catch (e) {
      developer.log('AwsAuthService: Sign in failed: $e');
      AppLogger.error('Sign in failed', error: e);

      String errorMessage;
      if (e is ApiException) {
        errorMessage = e.userFriendlyMessage;
      } else {
        errorMessage = e.toString();
      }

      return AwsAuthResponse(success: false, message: errorMessage);
    }
  }

  /// Sign out
  Future<void> signOut() async {
    // JWT tokens are stateless, so we just clear local data
    // No need to call backend signout endpoint
    await _clearAuthData();

    // Notify listeners
    _authStateController.add(AuthState.signedOut);

    developer.log('AwsAuthService: Sign out completed');
  }

  /// Restore session from stored tokens
  Future<bool> restoreSession() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      _accessToken = prefs.getString(_accessTokenKey);
      _refreshToken = prefs.getString(_refreshTokenKey);
      _idToken = prefs.getString(_idTokenKey);

      developer.log('AwsAuthService: Restoring session...');
      developer.log(
        'AwsAuthService: Access token length: ${_accessToken?.length ?? 0}',
      );
      developer.log(
        'AwsAuthService: Refresh token available: ${_refreshToken != null}',
      );
      if (_accessToken != null) {
        developer.log(
          'AwsAuthService: Access token starts with: ${_accessToken!.substring(0, math.min(50, _accessToken!.length))}...',
        );

        // Validate token format immediately after loading
        final tokenParts = _accessToken!.split('.');
        if (tokenParts.length != 3) {
          developer.log(
            'AwsAuthService: Stored token is corrupted, clearing auth data',
          );
          await _clearAuthData();
          return false;
        }
      }

      final userDataJson = prefs.getString(_userDataKey);
      if (userDataJson != null) {
        final userData = json.decode(userDataJson) as Map<String, dynamic>;
        _currentUser = AwsUser.fromJson(userData);
      }

      final tokenExpirationString = prefs.getString(_tokenExpirationKey);
      if (tokenExpirationString != null) {
        _tokenExpiration = DateTime.parse(tokenExpirationString);
      }

      if (_accessToken != null && _currentUser != null) {
        // Validate the token with the backend
        final isValid = await validateToken();
        if (isValid) {
          developer.log(
            'AwsAuthService: Session restored and validated successfully',
          );
          _authStateController.add(AuthState.signedIn);
          return true;
        } else {
          developer.log(
            'AwsAuthService: Stored token is invalid, attempting refresh',
          );
          // Try to refresh the token before giving up
          final refreshSuccess = await refreshToken();
          if (refreshSuccess) {
            developer.log(
              'AwsAuthService: Token refresh successful, session restored',
            );
            _authStateController.add(AuthState.signedIn);
            return true;
          } else {
            developer.log(
              'AwsAuthService: Token refresh failed, clearing session',
            );
            await _clearAuthData();
            return false;
          }
        }
      } else {
        developer.log('AwsAuthService: No valid session found');
        return false;
      }
    } catch (e) {
      developer.log('AwsAuthService: Failed to restore session: $e');
      await _clearAuthData();
      return false;
    }
  }

  /// Validate current access token with backend
  Future<bool> validateToken() async {
    if (_accessToken == null) {
      developer.log('AwsAuthService: No access token to validate');
      return false;
    }

    try {
      final response = await ApiService.instance.makeRequest(
        method: 'GET',
        path: '/auth/validate',
        accessToken: _accessToken,
      );

      final data = ApiService.instance.parseResponse(response);

      if (data['valid'] == true) {
        // Update user data if provided
        final userData = data['user'] as Map<String, dynamic>?;
        if (userData != null) {
          _currentUser = AwsUser.fromJson(userData);
          await _storeAuthData(); // Update stored user data
        }

        developer.log('AwsAuthService: Token validation successful');
        return true;
      } else {
        developer.log(
          'AwsAuthService: Token validation failed - response indicates invalid',
        );
        return false;
      }
    } catch (e) {
      developer.log('AwsAuthService: Token validation error: $e');
      // Check if it's a 401 (unauthorized) or 403 (forbidden) - these indicate invalid token
      if (e.toString().contains('401') || e.toString().contains('403')) {
        developer.log('AwsAuthService: Token is definitely invalid (401/403)');
        return false;
      }
      // For other errors (network, 404, 500, etc.), assume token might still be valid
      developer.log(
        'AwsAuthService: Token validation failed due to other error, assuming token is still valid',
      );
      return true;
    }
  }

  /// Refresh access token
  Future<bool> refreshToken() async {
    if (_refreshToken == null) {
      developer.log('AwsAuthService: No refresh token available');
      return false;
    }

    try {
      developer.log('AwsAuthService: Attempting token refresh...');
      final response = await ApiService.instance.makeRequest(
        method: 'POST',
        path: '/auth/refresh',
        body: {'refreshToken': _refreshToken},
      );

      final data = ApiService.instance.parseResponse(response);
      final tokens = data['tokens'] as Map<String, dynamic>?;

      if (tokens != null) {
        final newAccessToken = tokens['accessToken'] as String?;
        developer.log(
          'AwsAuthService: Received new access token, length: ${newAccessToken?.length ?? 0}',
        );

        _accessToken = newAccessToken;
        // Note: refresh token might be rotated
        if (tokens['refreshToken'] != null) {
          _refreshToken = tokens['refreshToken'] as String?;
          developer.log('AwsAuthService: Refresh token was rotated');
        }
        if (tokens['idToken'] != null) {
          _idToken = tokens['idToken'] as String?;
        }

        // Extract token expiration from new access token
        if (_accessToken != null) {
          _tokenExpiration = _extractTokenExpiration(_accessToken!);
          developer.log(
            'AwsAuthService: New token expires at: $_tokenExpiration',
          );
        }

        await _storeAuthData();
        developer.log('AwsAuthService: Token refresh successful');
        return true;
      }

      developer.log('AwsAuthService: No tokens in refresh response');
      return false;
    } catch (e) {
      developer.log('AwsAuthService: Token refresh failed: $e');
      await _clearAuthData();
      _authStateController.add(AuthState.signedOut);
      return false;
    }
  }

  /// Extract expiration time from JWT token
  DateTime? _extractTokenExpiration(String token) {
    try {
      // JWT tokens have 3 parts separated by dots: header.payload.signature
      final parts = token.split('.');
      if (parts.length != 3) return null;

      // Decode the payload (second part)
      String payload = parts[1];

      // Add padding if needed for base64 decoding
      while (payload.length % 4 != 0) {
        payload += '=';
      }

      final decodedBytes = base64Url.decode(payload);
      final decodedPayload = utf8.decode(decodedBytes);
      final payloadJson = json.decode(decodedPayload) as Map<String, dynamic>;

      // Extract 'exp' claim (expiration time as Unix timestamp)
      final exp = payloadJson['exp'] as int?;
      if (exp != null) {
        return DateTime.fromMillisecondsSinceEpoch(exp * 1000);
      }

      return null;
    } catch (e) {
      developer.log('AwsAuthService: Failed to extract token expiration: $e');
      return null;
    }
  }

  /// Store authentication data
  Future<void> _storeAuthData() async {
    final prefs = await SharedPreferences.getInstance();

    if (_accessToken != null) {
      // Validate token format before storing
      final tokenParts = _accessToken!.split('.');
      if (tokenParts.length == 3) {
        await prefs.setString(_accessTokenKey, _accessToken!);
        developer.log('AwsAuthService: Stored valid access token');
      } else {
        developer.log(
          'AwsAuthService: Refusing to store invalid access token format',
        );
      }
    }
    if (_refreshToken != null) {
      await prefs.setString(_refreshTokenKey, _refreshToken!);
    }
    if (_idToken != null) {
      await prefs.setString(_idTokenKey, _idToken!);
    }
    if (_currentUser != null) {
      await prefs.setString(_userDataKey, json.encode(_currentUser!.toJson()));
    }
    if (_tokenExpiration != null) {
      await prefs.setString(
        _tokenExpirationKey,
        _tokenExpiration!.toIso8601String(),
      );
    }
  }

  /// Store Apple authentication data
  Future<void> storeAppleAuthData(
    UserModel user,
    String accessToken,
    String refreshToken,
    String idToken,
  ) async {
    try {
      _currentUser = AwsUser(
        id: user.id,
        email: user.email,
        username: user.username,
      );
      _accessToken = accessToken;
      _refreshToken = refreshToken;
      _idToken = idToken;

      await _storeAuthData();
      developer.log('AwsAuthService: Apple auth data stored successfully');
    } catch (e) {
      developer.log('AwsAuthService: Error storing Apple auth data: $e');
      rethrow;
    }
  }

  /// Store Xbox authentication data
  Future<void> storeXboxAuthData(
    UserModel user,
    String accessToken,
    String refreshToken,
    String idToken,
  ) async {
    try {
      _currentUser = AwsUser(
        id: user.id,
        email: user.email,
        username: user.username,
      );
      _accessToken = accessToken;
      _refreshToken = refreshToken;
      _idToken = idToken;

      await _storeAuthData();
      developer.log('AwsAuthService: Xbox auth data stored successfully');
    } catch (e) {
      developer.log('AwsAuthService: Error storing Xbox auth data: $e');
      rethrow;
    }
  }

  /// Clear authentication data
  Future<void> _clearAuthData() async {
    final prefs = await SharedPreferences.getInstance();

    await prefs.remove(_accessTokenKey);
    await prefs.remove(_refreshTokenKey);
    await prefs.remove(_idTokenKey);
    await prefs.remove(_userDataKey);
    await prefs.remove(_tokenExpirationKey);

    _accessToken = null;
    _refreshToken = null;
    _idToken = null;
    _currentUser = null;
    _tokenExpiration = null;
  }

  /// Resend verification email
  Future<Map<String, dynamic>> resendVerificationEmail(String email) async {
    try {
      final response = await ApiService.instance.makeRequest(
        method: 'POST',
        path: '/auth/resend-verification',
        body: {'email': email},
      );

      final data = ApiService.instance.parseResponse(response);
      return {
        'success': true,
        'message': data['message'] ?? 'Verification email sent successfully',
      };
    } catch (e) {
      developer.log('AwsAuthService: Error resending verification email: $e');

      // Parse error message from API response
      String errorMessage = 'Failed to send verification email';
      String? details;

      if (e.toString().contains('User not found')) {
        errorMessage = 'No account found with this email address';
      } else if (e.toString().contains('Too many requests')) {
        errorMessage = 'Too many verification emails sent';
        details = 'Please wait before requesting another verification email';
      } else if (e.toString().contains('User already confirmed')) {
        errorMessage = 'Account already verified';
        details = 'You can sign in normally';
      } else if (e.toString().contains('Invalid request')) {
        errorMessage = 'Invalid email format';
      }

      return {'success': false, 'error': errorMessage, 'details': details};
    }
  }

  /// Confirm email verification with code
  Future<Map<String, dynamic>> confirmSignup(
    String email,
    String confirmationCode,
  ) async {
    try {
      final response = await ApiService.instance.makeRequest(
        method: 'POST',
        path: '/auth/confirm-signup',
        body: {'email': email, 'confirmationCode': confirmationCode},
      );

      final data = ApiService.instance.parseResponse(response);
      return {
        'success': true,
        'message': data['message'] ?? 'Email verified successfully',
        'verified': data['verified'] ?? true,
      };
    } catch (e) {
      developer.log('AwsAuthService: Error confirming signup: $e');

      // Parse error message from API response
      String errorMessage = 'Failed to verify email';
      String? details;

      if (e.toString().contains('Invalid verification code')) {
        errorMessage = 'Invalid verification code';
        details = 'Please check the code and try again';
      } else if (e.toString().contains('Verification code expired')) {
        errorMessage = 'Verification code expired';
        details = 'Please request a new verification code';
      } else if (e.toString().contains('User not found')) {
        errorMessage = 'Account not found';
        details = 'Please check your email address';
      } else if (e.toString().contains('User already confirmed')) {
        errorMessage = 'Account already verified';
        details = 'You can sign in normally';
      } else if (e.toString().contains('Too many attempts')) {
        errorMessage = 'Too many verification attempts';
        details = 'Please try again later';
      }

      return {'success': false, 'error': errorMessage, 'details': details};
    }
  }

  /// Initiate forgot password flow
  Future<AwsAuthResponse> forgotPassword(String email) async {
    try {
      developer.log('AwsAuthService: Starting forgot password for: $email');
      AppLogger.auth('Starting forgot password for: $email');

      final response = await ApiService.instance.makeRequest(
        method: 'POST',
        path: '/auth/forgot-password',
        body: {'email': email},
      );

      final data = ApiService.instance.parseResponse(response);

      developer.log('AwsAuthService: Forgot password email sent successfully');
      AppLogger.auth('Forgot password email sent successfully');

      return AwsAuthResponse(
        success: true,
        message: data['message'] as String? ?? 'Password reset email sent',
      );
    } catch (e) {
      developer.log('AwsAuthService: Forgot password failed: $e');
      AppLogger.error('Forgot password failed', error: e);

      String errorMessage;
      if (e is ApiException) {
        errorMessage = e.userFriendlyMessage;
      } else {
        errorMessage = e.toString();
      }

      return AwsAuthResponse(success: false, message: errorMessage);
    }
  }

  /// Confirm forgot password with verification code and new password
  Future<AwsAuthResponse> confirmForgotPassword({
    required String email,
    required String confirmationCode,
    required String newPassword,
  }) async {
    try {
      developer.log('AwsAuthService: Confirming forgot password for: $email');
      AppLogger.auth('Confirming forgot password for: $email');

      final response = await ApiService.instance.makeRequest(
        method: 'POST',
        path: '/auth/confirm-forgot-password',
        body: {
          'email': email,
          'confirmationCode': confirmationCode,
          'newPassword': newPassword,
        },
      );

      final data = ApiService.instance.parseResponse(response);

      developer.log('AwsAuthService: Password reset confirmed successfully');
      AppLogger.auth('Password reset confirmed successfully');

      return AwsAuthResponse(
        success: true,
        message: data['message'] as String? ?? 'Password reset successfully',
      );
    } catch (e) {
      developer.log('AwsAuthService: Confirm forgot password failed: $e');
      AppLogger.error('Confirm forgot password failed', error: e);

      String errorMessage;
      if (e is ApiException) {
        errorMessage = e.userFriendlyMessage;
      } else {
        errorMessage = e.toString();
      }

      return AwsAuthResponse(success: false, message: errorMessage);
    }
  }

  /// Dispose resources
  void dispose() {
    _authStateController.close();
  }
}

/// Authentication state enum
enum AuthState { signedIn, signedOut }

/// AWS authentication response
class AwsAuthResponse {
  final bool success;
  final String message;
  final AwsUser? user;
  final Map<String, dynamic>? tokens;
  final bool? requiresUsername;

  AwsAuthResponse({
    required this.success,
    required this.message,
    this.user,
    this.tokens,
    this.requiresUsername,
  });
}

/// AWS user model
class AwsUser {
  final String id;
  final String email;
  final String? username;
  final String? displayName;
  final String? avatarUrl;
  final bool isVerified;

  AwsUser({
    required this.id,
    required this.email,
    this.username,
    this.displayName,
    this.avatarUrl,
    this.isVerified = false,
  });

  factory AwsUser.fromJson(Map<String, dynamic> json) {
    return AwsUser(
      id: json['id'] as String,
      email: json['email'] as String,
      username: json['username'] as String?,
      displayName: json['display_name'] as String?,
      avatarUrl: json['avatar_url'] as String?,
      isVerified: json['is_verified'] as bool? ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'email': email,
      'username': username,
      'display_name': displayName,
      'avatar_url': avatarUrl,
      'is_verified': isVerified,
    };
  }
}
