import 'dart:developer' as developer;
import 'dart:io';
import 'package:http/http.dart' as http;
import '../utils/app_logger.dart';
import 'api_service.dart';

/// Enhanced media service that handles the complete upload and processing workflow
class EnhancedMediaService {
  static EnhancedMediaService? _instance;
  static EnhancedMediaService get instance =>
      _instance ??= EnhancedMediaService._();

  EnhancedMediaService._();

  /// Upload media with complete processing workflow and user-friendly error handling
  Future<MediaUploadResult> uploadMediaWithProcessing({
    required File file,
    required String fileName,
    required String fileType,
    String mediaType = 'image',
    Function(String)? onStatusUpdate,
  }) async {
    try {
      AppLogger.debug('EnhancedMediaService: Starting upload process');
      AppLogger.debug('EnhancedMediaService: File: $fileName');
      AppLogger.debug('EnhancedMediaService: Type: $fileType');
      AppLogger.debug('EnhancedMediaService: Media type: $mediaType');
      developer.log(
        'EnhancedMediaService: Starting enhanced media upload for $fileName',
      );
      onStatusUpdate?.call('Checking upload limits...');

      // Step 1: Request upload URL from backend (includes upload limits check)
      AppLogger.debug(
        'EnhancedMediaService: Requesting upload URL from backend',
      );
      final uploadUrlResponse = await _requestUploadUrl(
        fileName: fileName,
        fileType: fileType,
        fileSize: await file.length(),
        mediaType: mediaType,
      );

      if (uploadUrlResponse == null) {
        AppLogger.error(
          'EnhancedMediaService: Failed to get upload URL from backend',
        );
        return MediaUploadResult.error(
          'Failed to get upload URL',
          'Unable to prepare upload. Please try again.',
        );
      }

      final mediaId = uploadUrlResponse['mediaId'] as String;
      final uploadUrl = uploadUrlResponse['uploadUrl'] as String;
      AppLogger.debug(
        'EnhancedMediaService: Got upload URL for media ID: $mediaId',
      );

      developer.log(
        'EnhancedMediaService: Got upload URL for media ID: $mediaId',
      );
      onStatusUpdate?.call('Uploading file...');

      // Step 2: Upload file directly to S3
      AppLogger.debug('EnhancedMediaService: Starting file upload to S3');
      final uploadSuccess = await _uploadToS3(
        file: file,
        uploadUrl: uploadUrl,
        fileType: fileType,
      );

      if (!uploadSuccess) {
        AppLogger.error('EnhancedMediaService: File upload to S3 failed');
        return MediaUploadResult.error(
          'Failed to upload file',
          'Upload failed. Please check your connection and try again.',
        );
      }

      developer.log('EnhancedMediaService: File uploaded successfully to S3');
      AppLogger.debug('EnhancedMediaService: File uploaded successfully to S3');
      onStatusUpdate?.call('Processing content...');

      // Step 3: Update media status to 'uploaded' to trigger AI processing
      AppLogger.debug(
        'EnhancedMediaService: Updating media status to uploaded',
      );
      final statusUpdateSuccess = await _updateMediaStatus(
        mediaId: mediaId,
        status: 'uploaded',
      );

      if (!statusUpdateSuccess) {
        AppLogger.error(
          'EnhancedMediaService: Failed to update media status to uploaded',
        );
        return MediaUploadResult.error(
          'Failed to start processing',
          'Upload completed but processing failed to start. Please try again.',
        );
      }

      AppLogger.debug(
        'EnhancedMediaService: Media status updated to uploaded, starting AI processing wait',
      );
      onStatusUpdate?.call('Waiting for content review...');

      // Step 4: Wait for AI processing to complete
      final processingResult = await _waitForProcessingComplete(
        mediaId,
        onStatusUpdate: onStatusUpdate,
      );

      return processingResult;
    } catch (e) {
      developer.log('EnhancedMediaService: Error in enhanced upload: $e');
      AppLogger.error('Error in enhanced media upload', error: e);

      // Handle specific API exceptions with user-friendly messages
      if (e is ApiException) {
        return _handleApiException(e);
      }

      return MediaUploadResult.error(
        'Upload failed: ${e.toString()}',
        'An unexpected error occurred. Please try again.',
      );
    }
  }

  /// Handle API exceptions and convert them to user-friendly messages
  MediaUploadResult _handleApiException(ApiException e) {
    switch (e.statusCode) {
      case 429:
        // Upload limits exceeded
        final details = e.details;
        final reason = details['reason'] as String? ?? 'Upload limit exceeded';
        final activeDemerits =
            details['details']?['activeDemerits'] as int? ?? 0;
        final imagesInReview =
            details['details']?['imagesInReview'] as int? ?? 0;
        final uploadsInLastHour =
            details['details']?['uploadsInLastHour'] as int? ?? 0;

        String userMessage = _buildUploadLimitMessage(
          reason,
          activeDemerits,
          imagesInReview,
          uploadsInLastHour,
        );

        return MediaUploadResult.limitExceeded(
          reason,
          userMessage,
          activeDemerits: activeDemerits,
          imagesInReview: imagesInReview,
          uploadsInLastHour: uploadsInLastHour,
        );

      case 401:
        return MediaUploadResult.error(
          'Authentication failed',
          'Please log in again to continue uploading.',
        );

      case 403:
        return MediaUploadResult.error(
          'Access denied',
          'You don\'t have permission to upload content.',
        );

      default:
        return MediaUploadResult.error(e.message, e.userFriendlyMessage);
    }
  }

  /// Build user-friendly upload limit message
  String _buildUploadLimitMessage(
    String reason,
    int activeDemerits,
    int imagesInReview,
    int uploadsInLastHour,
  ) {
    if (reason.contains('demerits') || activeDemerits >= 10) {
      return '⚠️ Upload Suspended\n\n'
          'Your account has been temporarily suspended due to $activeDemerits recent content violations. '
          'Demerits expire after 30 days.\n\n'
          'If you believe this is an error, please contact support.';
    } else if (reason.contains('images in review') || imagesInReview >= 1) {
      return '⏳ Processing in Progress\n\n'
          'You currently have $imagesInReview image being processed. '
          'Please wait for it to complete before uploading another image.\n\n'
          'Processing usually takes 1-2 minutes.';
    } else if (reason.contains('uploads per hour') || uploadsInLastHour >= 20) {
      return '🕐 Hourly Limit Reached\n\n'
          'You\'ve reached the limit of 20 uploads per hour ($uploadsInLastHour/20). '
          'Please wait a bit before uploading more content.\n\n'
          'This limit resets every hour to ensure fair usage.';
    } else {
      return '🚫 Upload Limit Exceeded\n\n'
          'You\'ve reached an upload limit. Please try again later.\n\n'
          'Reason: $reason';
    }
  }

  /// Wait for AI processing to complete and return the final result
  Future<MediaUploadResult> _waitForProcessingComplete(
    String mediaId, {
    Function(String)? onStatusUpdate,
  }) async {
    const maxWaitTime = Duration(minutes: 5);
    const pollInterval = Duration(seconds: 3);
    final startTime = DateTime.now();
    int consecutiveFailures = 0;
    const maxConsecutiveFailures = 5;

    AppLogger.debug(
      'EnhancedMediaService: Starting processing wait for media ID: $mediaId',
    );

    while (DateTime.now().difference(startTime) < maxWaitTime) {
      try {
        AppLogger.debug(
          'EnhancedMediaService: Polling media info for ID: $mediaId',
        );
        final mediaInfo = await _getMediaInfo(mediaId);

        if (mediaInfo == null) {
          consecutiveFailures++;
          AppLogger.debug(
            'EnhancedMediaService: Media info is null, consecutive failures: $consecutiveFailures',
          );

          if (consecutiveFailures >= maxConsecutiveFailures) {
            AppLogger.error(
              'EnhancedMediaService: Too many consecutive failures getting media info',
            );
            return MediaUploadResult.error(
              'Failed to get media status',
              'Unable to check processing status. Please try again.',
            );
          }

          await Future.delayed(pollInterval);
          continue;
        }

        // Reset failure counter on successful response
        consecutiveFailures = 0;

        final status = mediaInfo['status'] as String?;
        final rejectionReason = mediaInfo['rejection_reason'] as String?;

        AppLogger.debug('EnhancedMediaService: Media status: $status');
        developer.log('EnhancedMediaService: Media status: $status');

        switch (status) {
          case 'approved':
            AppLogger.debug('EnhancedMediaService: Media approved');
            onStatusUpdate?.call('Content approved!');
            return MediaUploadResult.success(
              mediaId,
              'Content approved and ready to use!',
            );

          case 'rejected_inappropriate':
            AppLogger.debug(
              'EnhancedMediaService: Media rejected: $rejectionReason',
            );
            final userFriendlyReason = _buildRejectionMessage(rejectionReason);
            return MediaUploadResult.rejected(
              rejectionReason ?? 'Content flagged as inappropriate',
              userFriendlyReason,
            );

          case 'processing':
          case 'uploaded':
            AppLogger.debug('EnhancedMediaService: Media still processing');
            onStatusUpdate?.call('Analyzing content...');
            break;

          case 'failed':
            AppLogger.debug('EnhancedMediaService: Media processing failed');
            return MediaUploadResult.error(
              'Processing failed',
              'Content processing failed. Please try uploading again.',
            );

          default:
            AppLogger.debug('EnhancedMediaService: Unknown status: $status');
            onStatusUpdate?.call('Processing...');
        }

        await Future.delayed(pollInterval);
      } catch (e) {
        consecutiveFailures++;
        AppLogger.error(
          'EnhancedMediaService: Error checking processing status: $e',
        );
        developer.log(
          'EnhancedMediaService: Error checking processing status: $e',
        );

        if (consecutiveFailures >= maxConsecutiveFailures) {
          AppLogger.error('EnhancedMediaService: Too many consecutive errors');
          return MediaUploadResult.error(
            'Processing check failed',
            'Unable to check processing status. Please try again.',
          );
        }

        await Future.delayed(pollInterval);
      }
    }

    // Timeout
    AppLogger.debug('EnhancedMediaService: Processing timeout reached');
    return MediaUploadResult.error(
      'Processing timeout',
      'Content processing is taking longer than expected. Your upload may still complete in the background.',
    );
  }

  /// Build user-friendly rejection message with demerit warning
  String _buildRejectionMessage(String? rejectionReason) {
    final reason = rejectionReason ?? 'inappropriate content';

    return '🚫 Content Rejected\n\n'
        'Your upload was rejected due to: $reason\n\n'
        '⚠️ Important: This adds a demerit to your account. '
        'After 10 demerits, your upload privileges will be temporarily suspended.\n\n'
        'Please ensure your content follows our community guidelines. '
        'Gaming violence is allowed, but explicit or disturbing content is not.';
  }

  /// Request upload URL from backend
  Future<Map<String, dynamic>?> _requestUploadUrl({
    required String fileName,
    required String fileType,
    required int fileSize,
    required String mediaType,
  }) async {
    try {
      final response = await ApiService.instance.makeAuthenticatedRequest(
        method: 'POST',
        path: '/media/upload',
        body: {
          'fileName': fileName,
          'fileType': fileType,
          'fileSize': fileSize,
          'mediaType': mediaType,
        },
      );

      final data = ApiService.instance.parseResponse(response);
      return data;
    } catch (e) {
      developer.log('EnhancedMediaService: Error requesting upload URL: $e');
      rethrow; // Let the caller handle the ApiException
    }
  }

  /// Upload file directly to S3 using presigned URL
  Future<bool> _uploadToS3({
    required File file,
    required String uploadUrl,
    required String fileType,
  }) async {
    try {
      final fileBytes = await file.readAsBytes();
      AppLogger.debug(
        'EnhancedMediaService: File size: ${fileBytes.length} bytes',
      );
      AppLogger.debug('EnhancedMediaService: Content-Type: $fileType');
      AppLogger.debug(
        'EnhancedMediaService: Upload URL: ${uploadUrl.substring(0, 100)}...',
      );

      final response = await http.put(
        Uri.parse(uploadUrl),
        headers: {
          'Content-Type': fileType,
          'Content-Length': fileBytes.length.toString(),
        },
        body: fileBytes,
      );

      AppLogger.debug(
        'EnhancedMediaService: S3 response status: ${response.statusCode}',
      );
      AppLogger.debug(
        'EnhancedMediaService: S3 response headers: ${response.headers}',
      );

      if (response.statusCode == 200 || response.statusCode == 204) {
        developer.log('EnhancedMediaService: S3 upload successful');
        AppLogger.debug('EnhancedMediaService: S3 upload successful');
        return true;
      } else {
        developer.log(
          'EnhancedMediaService: S3 upload failed with status: ${response.statusCode}',
        );
        AppLogger.error(
          'EnhancedMediaService: S3 upload failed with status: ${response.statusCode}',
        );
        AppLogger.error(
          'EnhancedMediaService: S3 response body: ${response.body}',
        );
        return false;
      }
    } catch (e) {
      developer.log('EnhancedMediaService: Error uploading to S3: $e');
      AppLogger.error('EnhancedMediaService: Error uploading to S3: $e');
      return false;
    }
  }

  /// Update media status in backend
  Future<bool> _updateMediaStatus({
    required String mediaId,
    required String status,
  }) async {
    try {
      final response = await ApiService.instance.makeAuthenticatedRequest(
        method: 'PUT',
        path: '/media/$mediaId',
        body: {'status': status},
      );

      ApiService.instance.parseResponse(response);
      return true;
    } catch (e) {
      developer.log('EnhancedMediaService: Error updating media status: $e');
      return false;
    }
  }

  /// Get media information
  Future<Map<String, dynamic>?> _getMediaInfo(String mediaId) async {
    try {
      AppLogger.debug(
        'EnhancedMediaService: Making request to /media/$mediaId',
      );
      final response = await ApiService.instance.makeAuthenticatedRequest(
        method: 'GET',
        path: '/media/$mediaId',
      );

      AppLogger.debug('EnhancedMediaService: Got response for media info');
      final data = ApiService.instance.parseResponse(response);
      final mediaData = data['media'] as Map<String, dynamic>?;

      if (mediaData != null) {
        AppLogger.debug(
          'EnhancedMediaService: Media data retrieved successfully',
        );
      } else {
        AppLogger.debug('EnhancedMediaService: Media data is null in response');
      }

      return mediaData;
    } catch (e) {
      AppLogger.error('EnhancedMediaService: Error getting media info: $e');
      developer.log('EnhancedMediaService: Error getting media info: $e');
      return null;
    }
  }
}

/// Result class for media upload operations
class MediaUploadResult {
  final bool isSuccess;
  final bool isRejected;
  final bool isLimitExceeded;
  final String? mediaId;
  final String message;
  final String userFriendlyMessage;
  final int? activeDemerits;
  final int? imagesInReview;
  final int? uploadsInLastHour;

  MediaUploadResult._({
    required this.isSuccess,
    required this.isRejected,
    required this.isLimitExceeded,
    this.mediaId,
    required this.message,
    required this.userFriendlyMessage,
    this.activeDemerits,
    this.imagesInReview,
    this.uploadsInLastHour,
  });

  factory MediaUploadResult.success(String mediaId, String message) {
    return MediaUploadResult._(
      isSuccess: true,
      isRejected: false,
      isLimitExceeded: false,
      mediaId: mediaId,
      message: message,
      userFriendlyMessage: message,
    );
  }

  factory MediaUploadResult.rejected(
    String message,
    String userFriendlyMessage,
  ) {
    return MediaUploadResult._(
      isSuccess: false,
      isRejected: true,
      isLimitExceeded: false,
      message: message,
      userFriendlyMessage: userFriendlyMessage,
    );
  }

  factory MediaUploadResult.limitExceeded(
    String message,
    String userFriendlyMessage, {
    int? activeDemerits,
    int? imagesInReview,
    int? uploadsInLastHour,
  }) {
    return MediaUploadResult._(
      isSuccess: false,
      isRejected: false,
      isLimitExceeded: true,
      message: message,
      userFriendlyMessage: userFriendlyMessage,
      activeDemerits: activeDemerits,
      imagesInReview: imagesInReview,
      uploadsInLastHour: uploadsInLastHour,
    );
  }

  factory MediaUploadResult.error(String message, String userFriendlyMessage) {
    return MediaUploadResult._(
      isSuccess: false,
      isRejected: false,
      isLimitExceeded: false,
      message: message,
      userFriendlyMessage: userFriendlyMessage,
    );
  }
}
