import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/followed_posts_provider.dart';
import '../providers/auth_provider.dart';
import '../models/post_model.dart';
import '../theme/app_theme.dart';
import '../utils/app_logger.dart';
import '../services/media_preloader.dart';
import 'feed_item.dart';

class FollowedFeed extends StatefulWidget {
  final Function(PostModel)? onNavigateToReflexes;
  final Function(PostModel)? onCurrentPostChanged;

  const FollowedFeed({
    super.key,
    this.onNavigateToReflexes,
    this.onCurrentPostChanged,
  });

  @override
  State<FollowedFeed> createState() => _FollowedFeedState();
}

class _FollowedFeedState extends State<FollowedFeed> {
  late PageController _pageController;
  int _currentIndex = 0;

  @override
  void initState() {
    super.initState();
    _pageController = PageController();

    // Load followed posts when the widget is initialized, but only if authenticated
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final followedPostsProvider = Provider.of<FollowedPostsProvider>(
        context,
        listen: false,
      );

      if (authProvider.isAuthenticated) {
        if (followedPostsProvider.posts.isEmpty) {
          followedPostsProvider.loadPosts();
        } else {
          // Restore scroll position if posts are already loaded
          _restoreScrollPosition(followedPostsProvider);
        }
      }
    });
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  void _restoreScrollPosition(FollowedPostsProvider provider) {
    if (provider.posts.isNotEmpty &&
        provider.currentPostIndex < provider.posts.length) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (_pageController.hasClients) {
          _pageController.animateToPage(
            provider.currentPostIndex,
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeInOut,
          );
          setState(() {
            _currentIndex = provider.currentPostIndex;
          });
        }
      });
    }
  }

  void _scrollToTop() {
    AppLogger.debug('FollowedFeed: Scrolling to top after refresh');
    setState(() {
      _currentIndex = 0;
    });

    // Use a small delay to ensure the PageView is built
    Future.delayed(const Duration(milliseconds: 100), () {
      if (mounted && _pageController.hasClients) {
        _pageController.animateToPage(
          0,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<FollowedPostsProvider>(
      builder: (context, followedPostsProvider, child) {
        // Check if we need to scroll to top immediately when provider resets
        if (followedPostsProvider.currentPostIndex == 0 && _currentIndex != 0) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            _scrollToTop();
          });
        }
        AppLogger.debug(
          'FollowedFeed: Building with status ${followedPostsProvider.status}',
        );
        AppLogger.debug(
          'FollowedFeed: Posts count: ${followedPostsProvider.posts.length}',
        );

        // Show loading indicator for initial load
        if (followedPostsProvider.status == FollowedPostsStatus.loading &&
            followedPostsProvider.posts.isEmpty) {
          AppLogger.debug('FollowedFeed: Showing loading indicator');
          return const Center(
            child: CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(AppColors.gfGreen),
            ),
          );
        }

        // Show error state
        if (followedPostsProvider.status == FollowedPostsStatus.error) {
          AppLogger.debug('FollowedFeed: Showing error state');
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(
                  Icons.error_outline,
                  size: 64,
                  color: AppColors.gfGrayText,
                ),
                const SizedBox(height: 16),
                Text(
                  'Failed to load followed posts',
                  style: const TextStyle(
                    color: AppColors.gfGrayText,
                    fontSize: 18,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  followedPostsProvider.error ?? 'Unknown error',
                  style: const TextStyle(
                    color: AppColors.gfGrayText,
                    fontSize: 14,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: () => followedPostsProvider.loadPosts(),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.gfGreen,
                    foregroundColor: AppColors.gfDarkBlue,
                  ),
                  child: const Text('Retry'),
                ),
              ],
            ),
          );
        }

        // Show empty state if no followed posts
        if (followedPostsProvider.posts.isEmpty) {
          AppLogger.debug('FollowedFeed: Showing empty state');
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(
                  Icons.people_outline,
                  size: 64,
                  color: AppColors.gfGrayText,
                ),
                const SizedBox(height: 16),
                const Text(
                  'No posts from followed users',
                  style: TextStyle(
                    color: AppColors.gfGrayText,
                    fontSize: 18,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 8),
                const Text(
                  'Follow users and join channels to see their posts here',
                  style: TextStyle(color: AppColors.gfGrayText, fontSize: 14),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: () => followedPostsProvider.refreshPosts(),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.gfGreen,
                    foregroundColor: AppColors.gfDarkBlue,
                  ),
                  child: const Text('Refresh'),
                ),
              ],
            ),
          );
        }

        // Show posts feed
        AppLogger.debug(
          'FollowedFeed: Showing posts feed with ${followedPostsProvider.posts.length} posts',
        );
        return Stack(
          children: [
            // Refresh indicator
            RefreshIndicator(
              onRefresh: followedPostsProvider.refreshPosts,
              color: AppColors.gfGreen,
              backgroundColor: AppColors.gfDarkBackground,
              child: PageView.builder(
                controller: _pageController,
                scrollDirection: Axis.vertical,
                itemCount: followedPostsProvider.posts.length,
                physics: const ClampingScrollPhysics(),
                onPageChanged: (index) {
                  AppLogger.debug('FollowedFeed: Page changed to index $index');
                  setState(() {
                    _currentIndex = index;
                  });

                  // Save current index to provider for state persistence
                  followedPostsProvider.setCurrentPostIndex(index);

                  // Notify parent about current post change
                  if (widget.onCurrentPostChanged != null &&
                      index < followedPostsProvider.posts.length) {
                    widget.onCurrentPostChanged!(
                      followedPostsProvider.posts[index],
                    );
                  }

                  // Preload upcoming media content
                  MediaPreloader.instance.preloadUpcomingMedia(
                    posts: followedPostsProvider.posts,
                    currentIndex: index,
                    context: context,
                  );

                  // Load more posts when approaching the end
                  if (index >= followedPostsProvider.posts.length - 2 &&
                      followedPostsProvider.hasMore &&
                      followedPostsProvider.status !=
                          FollowedPostsStatus.loading) {
                    followedPostsProvider.loadMorePosts();
                  }
                },
                itemBuilder: (context, index) {
                  if (index >= followedPostsProvider.posts.length) {
                    return const Center(
                      child: CircularProgressIndicator(
                        valueColor: AlwaysStoppedAnimation<Color>(
                          AppColors.gfGreen,
                        ),
                      ),
                    );
                  }

                  final post = followedPostsProvider.posts[index];
                  return FeedItem(
                    key: ValueKey('followed_feed_item_${post.id}'),
                    post: post,
                    isVisible: index == _currentIndex,
                  );
                },
              ),
            ),
          ],
        );
      },
    );
  }
}
