import 'package:flutter/material.dart';
import '../services/config_service.dart';

/// Development server URL dropdown widget
/// Only shown in development builds
class DevServerDropdown extends StatefulWidget {
  final VoidCallback? onServerChanged;

  const DevServerDropdown({super.key, this.onServerChanged});

  @override
  State<DevServerDropdown> createState() => _DevServerDropdownState();
}

class _DevServerDropdownState extends State<DevServerDropdown> {
  List<ServerUrlOption> _options = [];
  ServerUrlOption? _selectedOption;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadOptions();
  }

  Future<void> _loadOptions() async {
    setState(() {
      _isLoading = true;
    });

    try {
      _options = ConfigService.instance.getServerUrlOptions();
      final selectedOptionName =
          await ConfigService.instance.getSelectedUrlOption();

      // Find the selected option
      _selectedOption = _options.firstWhere(
        (option) => option.name == selectedOptionName,
        orElse: () => _options.first,
      );
    } catch (e) {
      debugPrint('Error loading server options: $e');
      _selectedOption = _options.isNotEmpty ? _options.first : null;
    }

    setState(() {
      _isLoading = false;
    });
  }

  Future<void> _onOptionChanged(ServerUrlOption? option) async {
    if (option == null || option == _selectedOption) return;

    setState(() {
      _selectedOption = option;
    });

    try {
      await ConfigService.instance.setServerUrl(option.url, option.name);

      // Notify parent widget that server changed
      widget.onServerChanged?.call();

      // Show confirmation
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Server changed to: ${option.name}'),
            duration: const Duration(seconds: 2),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      debugPrint('Error changing server: $e');

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to change server: $e'),
            duration: const Duration(seconds: 3),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // Only show in development mode
    if (!ConfigService.instance.isDevelopment) {
      return const SizedBox.shrink();
    }

    if (_isLoading) {
      return const Padding(
        padding: EdgeInsets.all(16.0),
        child: Center(
          child: SizedBox(
            width: 20,
            height: 20,
            child: CircularProgressIndicator(strokeWidth: 2),
          ),
        ),
      );
    }

    if (_options.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      padding: const EdgeInsets.all(12.0),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(8.0),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            children: [
              Icon(
                Icons.developer_mode,
                size: 16,
                color: Theme.of(context).colorScheme.primary,
              ),
              const SizedBox(width: 8),
              Text(
                'Development Server',
                style: Theme.of(context).textTheme.labelMedium?.copyWith(
                  color: Theme.of(context).colorScheme.primary,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const Spacer(),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: Color(
                    int.parse(
                          ConfigService.instance
                              .getEnvironmentColor()
                              .substring(1),
                          radix: 16,
                        ) +
                        0xFF000000,
                  ),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  ConfigService.instance.getEnvironmentName(),
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          DropdownButtonFormField<ServerUrlOption>(
            initialValue: _selectedOption,
            decoration: InputDecoration(
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 12,
                vertical: 8,
              ),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(6),
                borderSide: BorderSide(
                  color: Theme.of(
                    context,
                  ).colorScheme.outline.withValues(alpha: 0.3),
                ),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(6),
                borderSide: BorderSide(
                  color: Theme.of(
                    context,
                  ).colorScheme.outline.withValues(alpha: 0.3),
                ),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(6),
                borderSide: BorderSide(
                  color: Theme.of(context).colorScheme.primary,
                ),
              ),
            ),
            items:
                _options.map((option) {
                  return DropdownMenuItem<ServerUrlOption>(
                    value: option,
                    child: Container(
                      constraints: const BoxConstraints(maxHeight: 48),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            option.name,
                            style: Theme.of(context).textTheme.bodyMedium
                                ?.copyWith(fontWeight: FontWeight.w500),
                            overflow: TextOverflow.ellipsis,
                          ),
                          if (option.description.isNotEmpty)
                            Flexible(
                              child: Text(
                                option.description,
                                style: Theme.of(
                                  context,
                                ).textTheme.bodySmall?.copyWith(
                                  color: Theme.of(context).colorScheme.onSurface
                                      .withValues(alpha: 0.6),
                                ),
                                overflow: TextOverflow.ellipsis,
                                maxLines: 1,
                              ),
                            ),
                        ],
                      ),
                    ),
                  );
                }).toList(),
            onChanged: _onOptionChanged,
            isExpanded: true,
            icon: Icon(
              Icons.arrow_drop_down,
              color: Theme.of(
                context,
              ).colorScheme.onSurface.withValues(alpha: 0.6),
            ),
          ),
          if (_selectedOption != null && _selectedOption!.url != 'auto')
            Padding(
              padding: const EdgeInsets.only(top: 4),
              child: Text(
                'URL: ${_selectedOption!.url}',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(
                    context,
                  ).colorScheme.onSurface.withValues(alpha: 0.5),
                  fontFamily: 'monospace',
                ),
              ),
            ),
        ],
      ),
    );
  }
}

/// Compact version of the server dropdown for smaller spaces
class CompactDevServerDropdown extends StatefulWidget {
  final VoidCallback? onServerChanged;

  const CompactDevServerDropdown({super.key, this.onServerChanged});

  @override
  State<CompactDevServerDropdown> createState() =>
      _CompactDevServerDropdownState();
}

class _CompactDevServerDropdownState extends State<CompactDevServerDropdown> {
  String _selectedOptionName = '';

  @override
  void initState() {
    super.initState();
    _loadSelectedOption();
  }

  Future<void> _loadSelectedOption() async {
    try {
      final selectedOption =
          await ConfigService.instance.getSelectedUrlOption();
      setState(() {
        _selectedOptionName = selectedOption;
      });
    } catch (e) {
      debugPrint('Error loading selected option: $e');
    }
  }

  void _showServerDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Development Server'),
            content: SizedBox(
              width: double.maxFinite,
              child: DevServerDropdown(
                onServerChanged: () {
                  _loadSelectedOption();
                  widget.onServerChanged?.call();
                  Navigator.of(context).pop();
                },
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Close'),
              ),
            ],
          ),
    );
  }

  @override
  Widget build(BuildContext context) {
    // Only show in development mode
    if (!ConfigService.instance.isDevelopment) {
      return const SizedBox.shrink();
    }

    return InkWell(
      onTap: _showServerDialog,
      borderRadius: BorderRadius.circular(4),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          borderRadius: BorderRadius.circular(4),
          border: Border.all(
            color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.developer_mode,
              size: 14,
              color: Theme.of(context).colorScheme.primary,
            ),
            const SizedBox(width: 4),
            Text(
              _selectedOptionName.isNotEmpty ? _selectedOptionName : 'Server',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(context).colorScheme.primary,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(width: 2),
            Icon(
              Icons.arrow_drop_down,
              size: 14,
              color: Theme.of(context).colorScheme.primary,
            ),
          ],
        ),
      ),
    );
  }
}
