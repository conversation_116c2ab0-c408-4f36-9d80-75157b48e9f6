import 'package:flutter/foundation.dart';
import 'dart:developer' as developer;
import '../services/aws_user_service.dart';
import '../models/post_model.dart';
import '../services/aws_auth_service.dart';
import '../utils/app_logger.dart';

/// User profile status enumeration
enum UserProfileStatus { initial, loading, loaded, refreshing, error }

/// Provider for managing user profile state
class UserProfileProvider extends ChangeNotifier {
  UserProfileStatus _status = UserProfileStatus.initial;
  Map<String, dynamic>? _userProfile;
  Map<String, dynamic>? _userStats;
  List<PostModel> _userPosts = [];
  String? _errorMessage;
  String? _currentUserId;
  bool _disposed = false;

  // Getters
  UserProfileStatus get status => _status;
  Map<String, dynamic>? get userProfile => _userProfile;
  Map<String, dynamic>? get userStats => _userStats;
  List<PostModel> get userPosts => _userPosts;
  String? get error => _errorMessage;
  String? get currentUserId => _currentUserId;
  bool get isLoading => _status == UserProfileStatus.loading;
  bool get isRefreshing => _status == UserProfileStatus.refreshing;

  /// Load user profile data
  Future<void> loadUserProfile(String userId, {bool loadPosts = true}) async {
    if (_status == UserProfileStatus.loading) return;

    developer.log('UserProfileProvider: Loading profile for user: $userId');
    AppLogger.user('Loading profile for user: $userId');

    _currentUserId = userId;
    _setStatus(UserProfileStatus.loading);

    try {
      // Check if this is the current user's profile
      final currentUser = AwsAuthService.instance.currentUser;
      final isCurrentUser = currentUser?.id == userId;

      // Get user profile info (includes user, profile, and stats)
      final userInfo =
          isCurrentUser
              ? await AwsUserService.instance.getCurrentUserProfile()
              : await AwsUserService.instance.getUserInfo(userId);

      if (userInfo != null) {
        _userProfile = userInfo;
        _userStats = userInfo['stats'] as Map<String, dynamic>?;

        developer.log(
          'UserProfileProvider: Successfully loaded user profile (current user: $isCurrentUser)',
        );
        AppLogger.user(
          'Successfully loaded user profile (current user: $isCurrentUser)',
        );
      } else {
        throw Exception('User profile not found');
      }

      // Load user posts only if requested
      if (loadPosts) {
        await _loadUserPosts(userId);
      }

      _setStatus(UserProfileStatus.loaded);
      developer.log('UserProfileProvider: Profile loading completed');
      AppLogger.user('Profile loading completed');
    } catch (e, stackTrace) {
      developer.log(
        'UserProfileProvider: Error loading profile',
        error: e,
        stackTrace: stackTrace,
      );
      AppLogger.error(
        'UserProfileProvider ERROR',
        error: e,
        stackTrace: stackTrace,
      );
      _setError('Failed to load user profile: $e');
    }
  }

  /// Load user posts publicly accessible method
  Future<void> loadUserPosts() async {
    if (_currentUserId == null) return;
    await _loadUserPosts(_currentUserId!);
  }

  /// Load user posts
  Future<void> _loadUserPosts(String userId) async {
    try {
      developer.log('UserProfileProvider: Loading posts for user: $userId');

      final postsData = await AwsUserService.instance.getUserPosts(userId);

      // Convert to PostModel objects
      _userPosts =
          postsData
              .map((postData) {
                try {
                  return PostModel.fromJson(postData);
                } catch (e) {
                  developer.log('UserProfileProvider: Error parsing post: $e');
                  return null;
                }
              })
              .where((post) => post != null)
              .cast<PostModel>()
              .toList();

      developer.log('UserProfileProvider: Loaded ${_userPosts.length} posts');
      AppLogger.user('Loaded ${_userPosts.length} posts');
    } catch (e) {
      developer.log('UserProfileProvider: Error loading user posts: $e');
      AppLogger.error('Error loading user posts', error: e);
      // Don't fail the entire profile load if posts fail
      _userPosts = [];
    }
  }

  /// Refresh user profile data
  Future<void> refreshProfile() async {
    if (_currentUserId == null) return;

    developer.log('UserProfileProvider: Refreshing profile');
    AppLogger.user('Refreshing profile');

    _setStatus(UserProfileStatus.refreshing);

    try {
      // Check if this is the current user's profile
      final currentUser = AwsAuthService.instance.currentUser;
      final isCurrentUser = currentUser?.id == _currentUserId;

      // Reload user profile info
      final userInfo =
          isCurrentUser
              ? await AwsUserService.instance.getCurrentUserProfile()
              : await AwsUserService.instance.getUserInfo(_currentUserId!);

      if (userInfo != null) {
        _userProfile = userInfo;
        _userStats = userInfo['stats'] as Map<String, dynamic>?;
      }

      // Reload user posts
      await _loadUserPosts(_currentUserId!);

      _setStatus(UserProfileStatus.loaded);
      developer.log('UserProfileProvider: Profile refresh completed');
      AppLogger.user('Profile refresh completed');
    } catch (e, stackTrace) {
      developer.log(
        'UserProfileProvider: Error refreshing profile',
        error: e,
        stackTrace: stackTrace,
      );
      AppLogger.error(
        'UserProfileProvider REFRESH ERROR',
        error: e,
        stackTrace: stackTrace,
      );
      _setError('Failed to refresh user profile: $e');
    }
  }

  /// Update user profile
  Future<bool> updateProfile({
    String? displayName,
    String? username,
    String? bio,
    String? avatarUrl,
    String? firstName,
    String? lastName,
    String? country,
    String? timezone,
    String? language,
  }) async {
    try {
      developer.log('UserProfileProvider: Updating profile');
      AppLogger.user('Updating profile');

      final success = await AwsUserService.instance.updateUserProfile(
        displayName: displayName,
        username: username,
        bio: bio,
        avatarUrl: avatarUrl,
        firstName: firstName,
        lastName: lastName,
        country: country,
        timezone: timezone,
        language: language,
      );

      if (success && _currentUserId != null) {
        // Refresh the profile data after successful update
        await refreshProfile();

        developer.log('UserProfileProvider: Profile updated successfully');
        AppLogger.user('Profile updated successfully');
        return true;
      } else {
        throw Exception('Failed to update profile');
      }
    } catch (e) {
      developer.log('UserProfileProvider: Error updating profile: $e');
      AppLogger.error('Error updating profile', error: e);
      _setError('Failed to update profile: $e');
      return false;
    }
  }

  /// Follow a user
  Future<bool> followUser(String userId) async {
    try {
      developer.log('UserProfileProvider: Following user: $userId');
      AppLogger.user('Following user: $userId');

      final success = await AwsUserService.instance.followUser(userId);

      if (success) {
        // Update the local follow status
        if (_userProfile != null && !_disposed) {
          _userProfile!['isFollowing'] = true;
          // Update followers count
          final stats = _userProfile!['stats'] as Map<String, dynamic>?;
          if (stats != null) {
            stats['followers'] = (stats['followers'] as int? ?? 0) + 1;
          }
          notifyListeners();
        }

        developer.log('UserProfileProvider: Successfully followed user');
        AppLogger.user('Successfully followed user');
        return true;
      }

      return false;
    } catch (e) {
      developer.log('UserProfileProvider: Error following user: $e');
      AppLogger.error('Error following user', error: e);
      return false;
    }
  }

  /// Unfollow a user
  Future<bool> unfollowUser(String userId) async {
    try {
      developer.log('UserProfileProvider: Unfollowing user: $userId');
      AppLogger.user('Unfollowing user: $userId');

      final success = await AwsUserService.instance.unfollowUser(userId);

      if (success) {
        // Update the local follow status
        if (_userProfile != null && !_disposed) {
          _userProfile!['isFollowing'] = false;
          // Update followers count
          final stats = _userProfile!['stats'] as Map<String, dynamic>?;
          if (stats != null) {
            stats['followers'] = (stats['followers'] as int? ?? 1) - 1;
          }
          notifyListeners();
        }

        developer.log('UserProfileProvider: Successfully unfollowed user');
        AppLogger.user('Successfully unfollowed user');
        return true;
      }

      return false;
    } catch (e) {
      developer.log('UserProfileProvider: Error unfollowing user: $e');
      AppLogger.error('Error unfollowing user', error: e);
      return false;
    }
  }

  /// Check if current user is following this user
  bool get isFollowing {
    return _userProfile?['isFollowing'] as bool? ?? false;
  }

  /// Clear error message
  void clearError() {
    _errorMessage = null;
    if (_status == UserProfileStatus.error) {
      _setStatus(UserProfileStatus.loaded);
    }
  }

  /// Reset provider state
  void reset() {
    if (_disposed) return;
    _status = UserProfileStatus.initial;
    _userProfile = null;
    _userStats = null;
    _userPosts.clear();
    _errorMessage = null;
    _currentUserId = null;
    notifyListeners();
  }

  /// Set status and notify listeners
  void _setStatus(UserProfileStatus status) {
    if (_disposed) return;
    _status = status;
    if (status != UserProfileStatus.error) {
      _errorMessage = null;
    }
    notifyListeners();
  }

  /// Set error and notify listeners
  void _setError(String error) {
    if (_disposed) return;
    developer.log('UserProfileProvider: Setting error: $error');
    AppLogger.error('UserProfileProvider: ERROR SET: $error');
    _errorMessage = error;
    _status = UserProfileStatus.error;
    notifyListeners();
  }

  @override
  void dispose() {
    _disposed = true;
    super.dispose();
  }
}
